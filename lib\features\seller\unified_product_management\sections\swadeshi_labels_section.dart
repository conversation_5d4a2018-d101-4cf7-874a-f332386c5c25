import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_form_bloc.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/widgets/app_common_widgets.dart';

/// Swadeshi Labels section for product form
class SwadeshiLabelsSection extends StatefulWidget {
  final UnifiedProductFormBloc formBloc;
  final Map<String, dynamic> initialData;

  const SwadeshiLabelsSection({
    Key? key,
    required this.formBloc,
    required this.initialData,
  }) : super(key: key);

  @override
  State<SwadeshiLabelsSection> createState() => _SwadeshiLabelsSectionState();
}

class _SwadeshiLabelsSectionState extends State<SwadeshiLabelsSection> {
  bool? _swadeshiBrand;
  bool? _swadeshiMade;
  bool? _swadeshiOwned;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    _swadeshiBrand = widget.initialData['swadeshiBrand'];
    _swadeshiMade = widget.initialData['swadeshiMade'];
    _swadeshiOwned = widget.initialData['swadeshiOwned'];
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: 'Swadeshi Labels',
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: true,
      textButtonWidget: AppCommonWidgets.appBarTextButtonText(
        text: AppStrings.save,
      ),
      onTapTextButton: _onTapSave,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(),
          verticalSizedBox(24),
          _buildSwadeshiBrandSection(),
          verticalSizedBox(20),
          _buildSwadeshiMadeSection(),
          verticalSizedBox(20),
          _buildSwadeshiOwnedSection(),
          verticalSizedBox(32),
          _buildSwadeshiInfo(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.flag_outlined,
                color: AppColors.brandBlack,
                size: 24,
              ),
              horizontalSizedBox(8),
              Text(
                'Swadeshi Product Labels',
                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ],
          ),
          verticalSizedBox(8),
          Text(
            'Help customers identify Swadeshi products by providing accurate information about your product\'s origin and ownership. All labels are required.',
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
        ],
      ),
    );
  }

  Widget _buildSwadeshiBrandSection() {
    return _buildLabelSection(
      title: 'Swadeshi Brand',
      description: 'Is this a Swadeshi (Indian) brand?',
      value: _swadeshiBrand,
      onChanged: (value) {
        setState(() {
          _swadeshiBrand = value;
          _hasChanges = true;
        });
      },
      helpText: 'Select "Yes" if the brand is of Indian origin or owned by an Indian company.',
    );
  }

  Widget _buildSwadeshiMadeSection() {
    return _buildLabelSection(
      title: 'Swadeshi Made',
      description: 'Is this product made in India?',
      value: _swadeshiMade,
      onChanged: (value) {
        setState(() {
          _swadeshiMade = value;
          _hasChanges = true;
        });
      },
      helpText: 'Select "Yes" if the product is manufactured or assembled in India.',
    );
  }

  Widget _buildSwadeshiOwnedSection() {
    return _buildLabelSection(
      title: 'Swadeshi Owned',
      description: 'Is this product owned by an Indian entity?',
      value: _swadeshiOwned,
      onChanged: (value) {
        setState(() {
          _swadeshiOwned = value;
          _hasChanges = true;
        });
      },
      helpText: 'Select "Yes" if the product is owned by an Indian individual or company.',
    );
  }

  Widget _buildLabelSection({
    required String title,
    required String description,
    required bool? value,
    required Function(bool?) onChanged,
    required String helpText,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value != null ? AppColors.brandBlack.withOpacity(0.3) : AppColors.borderColor1,
          width: value != null ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                    .copyWith(fontWeight: FontWeight.w600),
              ),
              horizontalSizedBox(4),
              Text(
                '*',
                style: AppTextStyle.contentText0(textColor: Colors.red),
              ),
            ],
          ),
          verticalSizedBox(8),
          Text(
            description,
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
          verticalSizedBox(16),
          Row(
            children: [
              Expanded(
                child: _buildOptionButton(
                  label: 'Yes',
                  isSelected: value == true,
                  onTap: () => onChanged(true),
                  color: Colors.green,
                ),
              ),
              horizontalSizedBox(12),
              Expanded(
                child: _buildOptionButton(
                  label: 'No',
                  isSelected: value == false,
                  onTap: () => onChanged(false),
                  color: Colors.red,
                ),
              ),
            ],
          ),
          verticalSizedBox(12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: AppColors.writingColor2,
                ),
                horizontalSizedBox(8),
                Expanded(
                  child: Text(
                    helpText,
                    style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : AppColors.borderColor1,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isSelected) ...[
              Icon(
                Icons.check_circle,
                size: 20,
                color: color,
              ),
              horizontalSizedBox(8),
            ],
            Text(
              label,
              style: AppTextStyle.contentText0(
                textColor: isSelected ? color : AppColors.writingColor2,
              ).copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwadeshiInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.brandBlack.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.brandBlack.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: AppColors.brandBlack,
                size: 20,
              ),
              horizontalSizedBox(8),
              Text(
                'About Swadeshi Labels',
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                    .copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          verticalSizedBox(12),
          _buildInfoPoint('Swadeshi Brand: Indicates if the brand is of Indian origin'),
          _buildInfoPoint('Swadeshi Made: Shows if the product is manufactured in India'),
          _buildInfoPoint('Swadeshi Owned: Indicates if the product is owned by an Indian entity'),
          _buildInfoPoint('These labels help customers make informed choices about supporting Indian products'),
          _buildInfoPoint('Accurate labeling builds trust and helps promote authentic Swadeshi products'),
        ],
      ),
    );
  }

  Widget _buildInfoPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: const EdgeInsets.only(top: 8, right: 8),
            decoration: BoxDecoration(
              color: AppColors.brandBlack,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
            ),
          ),
        ],
      ),
    );
  }

  void _onTapSave() {
    if (_swadeshiBrand == null || _swadeshiMade == null || _swadeshiOwned == null) {
      CommonMethods.toastMessage('Please select all Swadeshi labels', context);
      return;
    }

    final data = {
      'swadeshiBrand': _swadeshiBrand,
      'swadeshiMade': _swadeshiMade,
      'swadeshiOwned': _swadeshiOwned,
    };

    widget.formBloc.updateSectionData(ProductFormSectionType.swadeshiLabels, data);
    
    CommonMethods.toastMessage('Swadeshi labels saved', context);
    Navigator.pop(context, data);
  }

  Future<bool> _onWillPop() async {
    if (_hasChanges) {
      final shouldPop = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Unsaved Changes'),
          content: Text('You have unsaved changes. Do you want to save them?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Discard'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context, false);
                _onTapSave();
              },
              child: Text('Save'),
            ),
          ],
        ),
      );
      return shouldPop ?? false;
    }
    return true;
  }
}
