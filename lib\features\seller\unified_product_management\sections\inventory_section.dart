import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_form_bloc.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/features/seller/inventory_options/inventory_options_screen.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/widgets/app_common_widgets.dart';

/// Inventory section that integrates with existing inventory options screen
class InventorySection extends StatefulWidget {
  final UnifiedProductFormBloc formBloc;
  final Map<String, dynamic> initialData;

  const InventorySection({
    Key? key,
    required this.formBloc,
    required this.initialData,
  }) : super(key: key);

  @override
  State<InventorySection> createState() => _InventorySectionState();
}

class _InventorySectionState extends State<InventorySection> {
  late Map<String, dynamic> _currentData;

  @override
  void initState() {
    super.initState();
    _currentData = Map.from(widget.initialData);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  AppBar _buildAppBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: 'Inventory & Pricing',
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: false,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(),
          verticalSizedBox(24),
          _buildInventoryOverview(),
          verticalSizedBox(20),
          _buildManageInventoryButton(),
          verticalSizedBox(24),
          _buildInventoryDetails(),
          verticalSizedBox(32),
          _buildInventoryInfo(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.inventory_2_outlined,
                color: AppColors.brandBlack,
                size: 24,
              ),
              horizontalSizedBox(8),
              Text(
                'Inventory Management',
                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ],
          ),
          verticalSizedBox(8),
          Text(
            'Configure your product inventory, pricing, and variants. Note: MRP, selling price, and stock are now managed through variants.',
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryOverview() {
    final hasOptions = _currentData['hasOptions'] ?? false;
    final variants = _currentData['variants'] as List<ProductVariant>? ?? [];
    final options = _currentData['options'] as Map<String, List<String>>? ?? {};

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Configuration',
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                .copyWith(fontWeight: FontWeight.w600),
          ),
          verticalSizedBox(12),
          _buildConfigurationRow(
            'Product Options',
            hasOptions ? 'Enabled' : 'Disabled',
            hasOptions ? AppColors.brandBlack : AppColors.writingColor2,
          ),
          if (hasOptions && options.isNotEmpty) ...[
            verticalSizedBox(8),
            _buildOptionsPreview(options),
          ],
          verticalSizedBox(8),
          _buildConfigurationRow(
            'Variants',
            '${variants.length} configured',
            variants.isNotEmpty ? AppColors.brandBlack : AppColors.writingColor2,
          ),
          if (variants.isNotEmpty) ...[
            verticalSizedBox(8),
            _buildVariantsPreview(variants),
          ],
        ],
      ),
    );
  }

  Widget _buildConfigurationRow(String label, String value, Color valueColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
        ),
        Text(
          value,
          style: AppTextStyle.contentText0(textColor: valueColor)
              .copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildOptionsPreview(Map<String, List<String>> options) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: options.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '${entry.key}: ',
                    style: AppTextStyle.smallText(textColor: AppColors.appBlack)
                        .copyWith(fontWeight: FontWeight.w600),
                  ),
                  TextSpan(
                    text: entry.value.join(', '),
                    style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildVariantsPreview(List<ProductVariant> variants) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: variants.take(3).map((variant) {
          final combinationText = variant.combinations.entries
              .map((e) => '${e.key}: ${e.value}')
              .join(', ');
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (combinationText.isNotEmpty)
                  Text(
                    combinationText,
                    style: AppTextStyle.smallText(textColor: AppColors.appBlack)
                        .copyWith(fontWeight: FontWeight.w500),
                  ),
                Row(
                  children: [
                    Text(
                      'MRP: ₹${variant.mrpPrice}',
                      style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
                    ),
                    horizontalSizedBox(12),
                    Text(
                      'Selling: ₹${variant.sellingPrice}',
                      style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
                    ),
                    horizontalSizedBox(12),
                    Text(
                      'Stock: ${variant.stock}',
                      style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
                    ),
                  ],
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildManageInventoryButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _openInventoryOptions,
        icon: Icon(Icons.settings, size: 20),
        label: Text('Manage Inventory & Variants'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          foregroundColor: AppColors.appWhite,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildInventoryDetails() {
    final variants = _currentData['variants'] as List<ProductVariant>? ?? [];
    
    if (variants.isEmpty) {
      return _buildNoVariantsState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Inventory Details',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(16),
        ...variants.map((variant) => _buildVariantCard(variant)).toList(),
      ],
    );
  }

  Widget _buildNoVariantsState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor1, style: BorderStyle.solid),
      ),
      child: Column(
        children: [
          Icon(
            Icons.inventory_outlined,
            size: 48,
            color: AppColors.writingColor2,
          ),
          verticalSizedBox(12),
          Text(
            'No Inventory Configured',
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                .copyWith(fontWeight: FontWeight.w600),
          ),
          verticalSizedBox(8),
          Text(
            'Set up your product inventory, pricing, and variants using the button above.',
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVariantCard(ProductVariant variant) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (variant.combinations.isNotEmpty) ...[
            Row(
              children: variant.combinations.entries.map((entry) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.brandBlack.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${entry.key}: ${entry.value}',
                    style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
                  ),
                );
              }).toList(),
            ),
            verticalSizedBox(12),
          ],
          Row(
            children: [
              Expanded(
                child: _buildPriceInfo('MRP', '₹${variant.mrpPrice}'),
              ),
              Expanded(
                child: _buildPriceInfo('Selling Price', '₹${variant.sellingPrice}'),
              ),
              Expanded(
                child: _buildPriceInfo('Stock', '${variant.stock}'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceInfo(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
        ),
        verticalSizedBox(4),
        Text(
          value,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
              .copyWith(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildInventoryInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.brandBlack.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.brandBlack.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.brandBlack,
                size: 20,
              ),
              horizontalSizedBox(8),
              Text(
                'Important Information',
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                    .copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          verticalSizedBox(12),
          _buildInfoPoint('MRP, selling price, and stock are now managed through product variants'),
          _buildInfoPoint('Each variant can have different pricing and stock levels'),
          _buildInfoPoint('Product options allow customers to choose between variants'),
          _buildInfoPoint('At least one variant is required for the product to be published'),
        ],
      ),
    );
  }

  Widget _buildInfoPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: const EdgeInsets.only(top: 8, right: 8),
            decoration: BoxDecoration(
              color: AppColors.brandBlack,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
            ),
          ),
        ],
      ),
    );
  }

  void _openInventoryOptions() async {
    // Create a temporary product for the inventory options screen
    final tempProduct = Product(
      productReference: widget.formBloc.formData.productReference ?? "temp",
      productName: widget.formBloc.formData.basicDetails['productName'] ?? '',
      brandName: widget.formBloc.formData.basicDetails['brandName'] ?? '',
      storeReference: widget.formBloc.formData.storeReference,
      storeid: widget.formBloc.formData.storeId,
      options: _currentData['options'],
      variants: (_currentData['variants'] as List<ProductVariant>?)
          ?.map((v) => v.toJson())
          .toList(),
    );

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InventoryOptionsScreen(
          storeReference: widget.formBloc.formData.storeReference,
          product: tempProduct,
          isFromUpdateStock: false,
        ),
      ),
    );

    if (result != null && result is Map<String, dynamic>) {
      setState(() {
        _currentData = {
          'hasOptions': result['hasMultipleOptions'] ?? false,
          'options': result['options'] ?? {},
          'variants': result['variants'] ?? [],
          'hasMultipleOptions': result['hasMultipleOptions'] ?? false,
        };
      });

      // Update the form bloc with the new data
      widget.formBloc.updateSectionData(
        ProductFormSectionType.inventory,
        _currentData,
      );

      CommonMethods.toastMessage('Inventory settings updated', context);
    }
  }
}
