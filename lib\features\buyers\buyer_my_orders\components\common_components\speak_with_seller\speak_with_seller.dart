import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/speak_with_seller/speak_with_seller_bloc.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Possible reason
class SpeakWithSeller extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final String title;
  final String subTitle;
  const SpeakWithSeller(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order,
      required this.title,
      required this.subTitle})
      : super(key: key);
  @override
  State<SpeakWithSeller> createState() => _SpeakWithSellerState();
}
//endregion

class _SpeakWithSellerState extends State<SpeakWithSeller> {
  //region Bloc
  late SpeakWithSellerBloc speakWithSellerBloc;
  //endregion

  //region Store Info
  SingleStoreInfoResponse? storeInfoResponse;
  bool isLoadingStoreInfo = false;
  //endregion

  //region Init
  @override
  void initState() {
    //print("In possible screen sub order list is ${widget.subOrderList.length}");
    speakWithSellerBloc = SpeakWithSellerBloc(
        context, widget.order, widget.buyerSubOrderBloc, widget.subOrderList);
    _fetchStoreInfo();
    super.initState();
  }
  //endregion

  //region Fetch Store Info
  Future<void> _fetchStoreInfo() async {
    if (widget.order.storeReference == null) return;

    setState(() {
      isLoadingStoreInfo = true;
    });

    try {
      final storeInfoService = SingleStoreInfoServices();
      storeInfoResponse = await storeInfoService
          .getSingleStoreInfo(widget.order.storeReference!);
    } catch (e) {
      // Handle error silently, messaging button will be disabled
    } finally {
      setState(() {
        isLoadingStoreInfo = false;
      });
    }
  }
  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

//region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main title
            Text(
              "Get an update from your seller if refund is delayed",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(20),

            // Possible reasons section
            Text(
              "Possible reasons for delay:",
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(10),

            // Bullet points
            _buildBulletPoint(
                "Delivery time to seller is more than what you expected ( incase of any logistics partner)"),
            _buildBulletPoint("Delay in delivery services"),
            _buildBulletPoint("Return delivery must have failed"),
            verticalSizedBox(20),

            // Recommendation text
            Text(
              "We recommend you to speak with the seller and clear this out.",
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
            verticalSizedBox(30),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    "Send a message",
                    onTap: _onSendMessageTap,
                    isLoading: isLoadingStoreInfo,
                  ),
                ),
                horizontalSizedBox(15),
                Expanded(
                  child: _buildActionButton(
                    "Call the Store",
                    onTap: _onCallStoreTap,
                    backgroundColor: AppColors.brandBlack,
                    textColor: AppColors.appWhite,
                    borderColor: AppColors.appBlack,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
//endregion

  //region Helper Methods
  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "• ",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String text, {
    required VoidCallback onTap,
    Color? backgroundColor,
    Color? textColor,
    Color? borderColor,
    bool isLoading = false,
  }) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.appBlack,
        borderRadius: BorderRadius.circular(25),
        border: borderColor != null
            ? Border.all(color: borderColor, width: 1)
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onTap,
          borderRadius: BorderRadius.circular(25),
          child: Center(
            child: isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? AppColors.appWhite,
                      ),
                    ),
                  )
                : Text(
                    text,
                    style: AppTextStyle.access0(
                      textColor: textColor ?? AppColors.appWhite,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  void _onSendMessageTap() {
    if (storeInfoResponse?.data?.newMessagingUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Store messaging is not available')),
      );
      return;
    }

    //Check if static user then open login screen
    if (CommonMethods().isStaticUser()) {
      CommonMethods().goToSignUpFlow();
      return;
    }

    NewMessagingChatScreen.navigateToChat(
      context,
      connectingId: storeInfoResponse!.data!.newMessagingUserId!,
      chatName: storeInfoResponse!.data!.storehandle!,
      chatIcon: storeInfoResponse!.data!.icon ?? '',
      entityType: 'STORE',
      chatOwnerReference: storeInfoResponse!.data!.storeReference
    );
  }

  void _onCallStoreTap() {
    final phoneNumbers =
        speakWithSellerBloc.subOrderList.first.storeContactInfo?.phoneNumber;

    if (phoneNumbers == null || phoneNumbers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Store phone number is not available')),
      );
      return;
    }

    // Use the first available phone number
    CommonMethods.openDialPad(phoneNumber: phoneNumbers.first);
  }
  //endregion
}
