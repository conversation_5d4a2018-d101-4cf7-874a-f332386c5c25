import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class GetOrdersCardBloc {
  // region Common Variables
  final BuildContext context;
  final String storeReference;

  // Controller for state management
  final _stateController = StreamController<bool>.broadcast();

  // Getter for state stream
  Stream<bool> get stateStream => _stateController.stream;

  // endregion

  // region Constructor
  GetOrdersCardBloc(this.context, this.storeReference);
  // endregion

  // region Open/Close Store
  Future<void> toggleStoreOpenStatus() async {
    try {
      // Get reference to the StoreDashboardDataModel
      final storeDashboardDataModel =
          Provider.of<StoreDashboardDataModel>(context, listen: false);

      // Check if store is active before allowing open/close
      if (!storeDashboardDataModel.storeDashBoard.isActive!) {
        CommonMethods.toastMessage(AppStrings.theStoreIsNotActiveYet, context,
            toastShowTimer: 5);
        return;
      }

      // Toggle the openForOrder status
      storeDashboardDataModel.storeDashBoard.openForOrder =
          !storeDashboardDataModel.storeDashBoard.openForOrder!;

      // Update UI
      storeDashboardDataModel.updateUi();
      _stateController
          .add(storeDashboardDataModel.storeDashBoard.openForOrder!);

      // Show status message
      CommonMethods.toastMessage(
        storeDashboardDataModel.storeDashBoard.openForOrder!
            ? AppStrings.yourStoreIsCurrentlyOpen
            : AppStrings.yourStoreIsCurrentlyClosed,
        context,
      );

      // Call API to update the status
      await StoreDashboardService()
          .openCloseStore(storeReference: storeReference);
    } on ApiErrorResponseMessage catch (error) {
      // Revert the change on error
      _revertStatusChange();
      CommonMethods.toastMessage(
          error.message ?? AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    } catch (error) {
      // Revert the change on error
      _revertStatusChange();
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    }
  }

  void _revertStatusChange() {
    final storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);
    storeDashboardDataModel.storeDashBoard.openForOrder =
        !storeDashboardDataModel.storeDashBoard.openForOrder!;
    storeDashboardDataModel.updateUi();
    _stateController.add(storeDashboardDataModel.storeDashBoard.openForOrder!);
  }
  // endregion

  // region Dispose
  void dispose() {
    _stateController.close();
  }
  // endregion
}
