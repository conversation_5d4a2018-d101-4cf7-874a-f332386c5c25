import 'dart:convert';
import 'dart:math' show min, max;
import 'package:http/http.dart' as http;
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/buyers/messaging/buffer/chat_buffer_manager.dart';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';

class Message {
  final String message_id;
  final String chat_id;
  final String sender_id;
  final String content;
  final String message_type;
  final Map<String, dynamic> metadata;
  final List<dynamic> attachments;
  final String? object;
  final int sequence_number;
  final DateTime created_at;
  final DateTime updated_at;
  final String? sender_name;

  Message({
    required this.message_id,
    required this.chat_id,
    required this.sender_id,
    required this.content,
    required this.message_type,
    required this.metadata,
    required this.attachments,
    this.object,
    required this.sequence_number,
    required this.created_at,
    required this.updated_at,
    this.sender_name,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    try {
      developer.log('[ENTER] Message.fromJson(): Parsing message data',
          name: 'Message');

      // First parse and validate required fields
      final message_id = json['message_id']?.toString();
      final chat_id = json['chat_id']?.toString();
      final sender_id = json['sender_id']?.toString();
      final content = json['content']?.toString();

      if (message_id == null ||
          chat_id == null ||
          sender_id == null ||
          content == null) {
        throw Exception('Required fields missing: ${json.toString()}');
      }

      // Parse optional fields with defaults
      final message_type = json['message_type']?.toString() ?? 'text';
      final metadata = json['metadata'] as Map<String, dynamic>? ?? {};
      final attachments = json['attachments'] as List<dynamic>? ?? [];
      final object = json['object']?.toString();

      // Parse numeric fields
      final sequence_number =
          int.tryParse(json['sequence_number']?.toString() ?? '0') ?? 0;

      // Parse date fields
      final created_at_str = json['created_at']?.toString();
      final updated_at_str = json['updated_at']?.toString();

      final created_at = created_at_str != null
          ? DateTime.tryParse(created_at_str) ?? DateTime.now()
          : DateTime.now();

      final updated_at = updated_at_str != null
          ? DateTime.tryParse(updated_at_str) ?? DateTime.now()
          : DateTime.now();

      developer.log(
          '[INFO] Message.fromJson(): Successfully parsed message data',
          name: 'Message');

      return Message(
        message_id: message_id,
        chat_id: chat_id,
        sender_id: sender_id,
        content: content,
        message_type: message_type,
        metadata: metadata,
        attachments: attachments,
        object: object,
        sequence_number: sequence_number,
        created_at: created_at,
        updated_at: updated_at,
        sender_name: json['sender_name']?.toString(),
      );
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] Message.fromJson(): Error parsing message: ${e.toString()}',
          name: 'Message',
          stackTrace: stackTrace);
      developer.log(
          '[ERROR] Message.fromJson(): Raw JSON that failed to parse: ${jsonEncode(json)}',
          name: 'Message');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    try {
      final Map<String, dynamic> json = {
        'message_id': message_id,
        'chat_id': chat_id,
        'sender_id': sender_id,
        'content': content,
        'message_type': message_type,
        'metadata': metadata,
        'attachments': attachments,
        'object': object,
        'sequence_number': sequence_number,
        'created_at': created_at.toIso8601String(),
        'updated_at': updated_at.toIso8601String(),
      };

      if (sender_name != null) {
        json['sender_name'] = sender_name;
      }

      return json;
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] Message.toJson(): Error serializing message: ${e.toString()}',
          name: 'Message',
          stackTrace: stackTrace);
      rethrow;
    }
  }

  factory Message.temporary({
    required String tempId,
    required String chatId,
    required String senderId,
    required String content,
    String messageType = 'text',
  }) {
    try {
      developer.log('[INFO] Message.temporary(): Creating temporary message',
          name: 'Message');

      return Message(
        message_id: tempId,
        chat_id: chatId,
        sender_id: senderId,
        content: content,
        message_type: messageType,
        metadata: {},
        attachments: [],
        object: null,
        sequence_number: 0, // Will be assigned by server
        created_at: DateTime.now(),
        updated_at: DateTime.now(),
        sender_name: null,
      );
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] Message.temporary(): Error creating temporary message: ${e.toString()}',
          name: 'Message',
          stackTrace: stackTrace);
      rethrow;
    }
  }
}

class ChatPreview {
  final DateTime? timestamp;
  final String? previewText;

  ChatPreview({
    required this.timestamp,
    required this.previewText,
  });

  factory ChatPreview.fromJson(Map<String, dynamic> json) {
    try {
      final timestampStr = json['timestamp'] as String?;
      final previewText = json['preview_text'] as String?;

      return ChatPreview(
        timestamp:
            timestampStr != null ? DateTime.tryParse(timestampStr) : null,
        previewText: previewText,
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] ChatPreview.fromJson(): Error parsing chat preview: ${e.toString()}',
        name: 'ChatPreview',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    try {
      return {
        'timestamp': timestamp?.toIso8601String(),
        'preview_text': previewText,
      };
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] ChatPreview.toJson(): Error serializing chat preview: ${e.toString()}',
        name: 'ChatPreview',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}

class ChatInfo {
  final String chat_id;
  String? chat_icon;
  String chat_name;
  final String chat_type;
  final String user_id;
  bool is_subscribed;
  int unread_count;
  DateTime? last_accessed;
  int last_read_sequence;
  final String entity_type;
  final DateTime created_at;
  DateTime updated_at;
  List<String>? member_ids;
  ChatPreview? chat_preview;
  String? role_type;
  bool is_muted;
  String? connecting_id;
  String? message_access;
  String? connecting_id_type;
  String? chat_owner_reference;

  ChatInfo({
    required this.chat_id,
    this.chat_icon,
    required this.chat_name,
    required this.chat_type,
    required this.user_id,
    required this.is_subscribed,
    required this.unread_count,
    this.last_accessed,
    required this.last_read_sequence,
    required this.entity_type,
    required this.created_at,
    required this.updated_at,
    this.member_ids,
    this.chat_preview,
    this.role_type,
    this.is_muted = false,
    this.connecting_id,
    this.message_access,
    this.connecting_id_type,
    this.chat_owner_reference,
  });

  /// Updates mutable fields from another ChatInfo instance
  void updateFrom(ChatInfo other) {
    if (chat_id != other.chat_id) {
      throw ArgumentError('Cannot update chat_id');
    }
    chat_icon = other.chat_icon;
    chat_name = other.chat_name;
    is_subscribed = other.is_subscribed;
    unread_count = other.unread_count;
    last_accessed = other.last_accessed;
    last_read_sequence = other.last_read_sequence;
    updated_at = other.updated_at;
    member_ids = other.member_ids;
    chat_preview = other.chat_preview;
    role_type = other.role_type;
    is_muted = other.is_muted;
    connecting_id = other.connecting_id;
    message_access = other.message_access;
    connecting_id_type = other.connecting_id_type;
    chat_owner_reference = other.chat_owner_reference;
  }

  factory ChatInfo.fromJson(Map<String, dynamic> json) {
    try {
      final chat_id = json['chat_id']?.toString() ?? '';
      if (chat_id.isEmpty) {
        throw FormatException('chat_id is required');
      }

      final chat_name = json['chat_name']?.toString() ?? '';
      if (chat_name.isEmpty) {
        throw FormatException('chat_name is required');
      }

      final chat_type = json['chat_type']?.toString() ?? '';
      if (chat_type.isEmpty) {
        throw FormatException('chat_type is required');
      }

      final user_id = json['user_id']?.toString() ?? '';
      if (user_id.isEmpty) {
        throw FormatException('user_id is required');
      }

      final entity_type = json['entity_type']?.toString() ?? '';
      if (entity_type.isEmpty) {
        throw FormatException('entity_type is required');
      }

      final is_subscribed = json['is_subscribed'] ?? false;
      final unread_count = json['unread_count'] ?? 0;
      final last_read_sequence = json['last_read_sequence'] ?? 0;

      final last_accessed = json['last_accessed'] != null
          ? DateTime.parse(json['last_accessed'])
          : null;

      final created_at = DateTime.parse(json['created_at']);
      final updated_at = DateTime.parse(json['updated_at']);

      List<String>? member_ids;
      if (json['member_ids'] != null) {
        member_ids = List<String>.from(json['member_ids']);
      }

      ChatPreview? chat_preview;
      if (json['chat_preview'] != null) {
        chat_preview = ChatPreview.fromJson(json['chat_preview']);
      }

      // Parse new fields
      final role_type = json['role_type']?.toString();
      final is_muted = json['is_muted'] ?? false;
      final connecting_id = json['connecting_id']?.toString();
      final message_access = json['message_access']?.toString();
      final connecting_id_type = json['connecting_id_type']?.toString();
      final chat_owner_reference = json['chat_owner_reference']?.toString();

      return ChatInfo(
        chat_id: chat_id,
        chat_icon: json['chat_icon']?.toString(),
        chat_name: chat_name,
        chat_type: chat_type,
        user_id: user_id,
        is_subscribed: is_subscribed,
        unread_count: unread_count,
        last_accessed: last_accessed,
        last_read_sequence: last_read_sequence,
        entity_type: entity_type,
        created_at: created_at,
        updated_at: updated_at,
        member_ids: member_ids,
        chat_preview: chat_preview,
        role_type: role_type,
        is_muted: is_muted,
        connecting_id: connecting_id,
        message_access: message_access,
        connecting_id_type: connecting_id_type,
        chat_owner_reference: chat_owner_reference,
      );
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] ChatInfo.fromJson(): Error parsing chat: ${e.toString()}',
          name: 'ChatInfo',
          stackTrace: stackTrace);
      developer.log(
          '[ERROR] ChatInfo.fromJson(): Raw JSON that failed to parse: ${jsonEncode(json)}',
          name: 'ChatInfo');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    try {
      final Map<String, dynamic> json = {
        'chat_id': chat_id,
        'chat_name': chat_name,
        'chat_type': chat_type,
        'user_id': user_id,
        'is_subscribed': is_subscribed,
        'unread_count': unread_count,
        'last_read_sequence': last_read_sequence,
        'entity_type': entity_type,
        'created_at': created_at.toIso8601String(),
        'updated_at': updated_at.toIso8601String(),
        'is_muted': is_muted,
      };

      if (chat_icon != null) json['chat_icon'] = chat_icon;
      if (last_accessed != null) {
        json['last_accessed'] = last_accessed!.toIso8601String();
      }
      if (member_ids != null) json['member_ids'] = member_ids;
      if (chat_preview != null) json['chat_preview'] = chat_preview!.toJson();
      if (role_type != null) json['role_type'] = role_type;
      if (connecting_id != null) json['connecting_id'] = connecting_id;
      if (message_access != null) json['message_access'] = message_access;
      if (connecting_id_type != null)
        json['connecting_id_type'] = connecting_id_type;
      if (chat_owner_reference != null)
        json['chat_owner_reference'] = chat_owner_reference;

      return json;
    } catch (e, stackTrace) {
      developer.log(
          '[ERROR] ChatInfo.toJson(): Error serializing chat: ${e.toString()}',
          name: 'ChatInfo',
          stackTrace: stackTrace);
      rethrow;
    }
  }
}

class MessageRepository {
  final List<dynamic> _messages = [];
  late final ChatBufferManager _bufferManager;
  Future<void>? _initializationFuture;

  MessageRepository() {
    _initializationFuture = _initializeBufferManager();
  }

  Future<void> _initializeBufferManager() async {
    try {
      developer.log(
          '[ENTER] _initializeBufferManager(): Initializing buffer manager',
          name: 'MessageRepository');

      final prefs = await SharedPreferences.getInstance();
      _bufferManager = ChatBufferManager(prefs);
      await _bufferManager.loadFromStorage();

      developer.log(
          '[EXIT] _initializeBufferManager(): Buffer manager initialized',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] _initializeBufferManager(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Ensures the buffer manager is initialized before proceeding
  Future<void> _ensureInitialized() async {
    try {
      if (_initializationFuture != null) {
        await _initializationFuture;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] _ensureInitialized(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Retrieves messages around a specific sequence number
  ///
  /// First checks buffer, then falls back to API if needed
  /// Properly merges and sorts messages from both sources
  Future<List<Message>> getMessagesAroundSequence(
      String chatId, int sequenceNumber) async {
    try {
      await _ensureInitialized();

      developer.log(
          '[ENTER] getMessagesAroundSequence(): Getting messages around sequence number $sequenceNumber',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'sequenceNumber': sequenceNumber,
          });

      // Get cached messages
      final cachedMessages = await _bufferManager.getMessages(chatId);

      developer.log(
          '[INFO] getMessagesAroundSequence(): Retrieved cached messages',
          name: 'MessageRepository',
          error: {
            'cachedMessageCount': cachedMessages.length,
          });

      // Fetch from API regardless of cache to ensure we have latest messages
      final queryParams = {
        'chat_id': chatId,
        'anchor_sequence': sequenceNumber.toString(),
        'direction': 'BI-DIRECTION',
        'limit': '40',
      };

      final uri = Uri.parse(AppConstants.newMessaging_getMessagesFromSequence)
          .replace(queryParameters: queryParams);

      developer.log('[INFO] getMessagesAroundSequence(): Making API request',
          name: 'MessageRepository',
          error: {
            'uri': uri.toString(),
            'queryParams': queryParams,
          });

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode != 200) {
        developer.log('[ERROR] getMessagesAroundSequence(): API request failed',
            name: 'MessageRepository',
            error: {
              'statusCode': response.statusCode,
              'body': response.body,
              'uri': uri.toString(),
              'queryParams': queryParams,
            });
        throw Exception(
            '[MessageRepository.getMessagesAroundSequence] Failed to load messages: ${response.statusCode} - ${response.body}');
      }

      try {
        final List<dynamic> data = jsonDecode(response.body);
        developer.log(
            '[INFO] getMessagesAroundSequence(): Messages loaded successfully',
            name: 'MessageRepository',
            error: {
              'messageCount': data.length,
            });

        final apiMessages = data.map((json) => Message.fromJson(json)).toList();

        developer.log(
            '[INFO] getMessagesAroundSequence(): Retrieved API messages',
            name: 'MessageRepository',
            error: {
              'chatId': chatId,
              'apiCount': apiMessages.length,
            });

        // Merge messages from both sources and remove duplicates
        final allMessages = [...cachedMessages, ...apiMessages];
        final uniqueMessages = <Message>[];
        final seenIds = <String>{};

        for (var msg in allMessages) {
          if (!seenIds.contains(msg.message_id)) {
            uniqueMessages.add(msg);
            seenIds.add(msg.message_id);
          }
        }

        // Sort by sequence number
        uniqueMessages
            .sort((a, b) => a.sequence_number.compareTo(b.sequence_number));

        // Update buffer with new messages
        await _bufferManager.addMessages(chatId, apiMessages);

        developer.log(
            '[EXIT] getMessagesAroundSequence(): Returning merged messages',
            name: 'MessageRepository',
            error: {
              'chatId': chatId,
              'totalCount': uniqueMessages.length,
              'fromCache': cachedMessages.length,
              'fromApi': apiMessages.length,
            });

        return uniqueMessages;
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] getMessagesAroundSequence(): Failed to parse response',
            name: 'MessageRepository',
            error: {
              'error': e.toString(),
              'body': response.body,
              'uri': uri.toString(),
            },
            stackTrace: stackTrace);
        rethrow;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] getMessagesAroundSequence(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Loads more messages, either older or newer
  ///
  /// Properly handles pagination and merging with buffer
  Future<List<Message>> loadMoreMessages(String chatId,
      {required bool older}) async {
    try {
      developer.log('[ENTER] loadMoreMessages(): Loading more messages',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'loadingOlder': older,
          });

      // Get cached messages and calculate sequence number
      final cachedMessages = await _bufferManager.getMessages(chatId);
      final lastSequence = cachedMessages.isEmpty
          ? 0
          : older
              ? cachedMessages.map((m) => m.sequence_number).reduce(min)
              : // Get oldest sequence
              cachedMessages
                  .map((m) => m.sequence_number)
                  .reduce(max); // Get newest sequence

      developer.log('[INFO] loadMoreMessages(): Last sequence calculated',
          name: 'MessageRepository',
          error: {
            'lastSequence': lastSequence,
            'cachedMessageCount': cachedMessages.length,
          });

      final queryParams = {
        'chat_id': chatId,
        'anchor_sequence': lastSequence.toString(),
        'direction': older ? 'PAST' : 'FUTURE',
        'limit': '20',
      };

      final uri = Uri.parse(AppConstants.newMessaging_getMessagesFromSequence)
          .replace(queryParameters: queryParams);

      developer.log('[INFO] loadMoreMessages(): Making API request',
          name: 'MessageRepository',
          error: {
            'uri': uri.toString(),
            'queryParams': queryParams,
          });

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode != 200) {
        developer.log('[ERROR] loadMoreMessages(): API request failed',
            name: 'MessageRepository',
            error: {
              'statusCode': response.statusCode,
              'body': response.body,
              'uri': uri.toString(),
              'queryParams': queryParams,
            });
        throw Exception(
            '[MessageRepository.loadMoreMessages] Failed to load messages: ${response.statusCode} - ${response.body}');
      }

      try {
        // First parse raw message with jsonDecode
        final List<dynamic> data = jsonDecode(response.body);
        developer.log('[INFO] loadMoreMessages(): Messages loaded successfully',
            name: 'MessageRepository',
            error: {
              'messageCount': data.length,
            });

        // Then parse each message and create Message objects
        final messages = data.map((json) => Message.fromJson(json)).toList();

        developer.log('[INFO] loadMoreMessages(): Messages parsed successfully',
            name: 'MessageRepository',
            error: {
              'parsedMessageCount': messages.length,
            });

        // Update buffer with parsed messages
        await _bufferManager.addMessages(chatId, messages);

        // Return parsed Message objects instead of raw data
        return messages;
      } catch (e, stackTrace) {
        developer.log('[ERROR] loadMoreMessages(): Failed to parse response',
            name: 'MessageRepository',
            error: {
              'error': e.toString(),
              'body': response.body,
              'uri': uri.toString(),
            },
            stackTrace: stackTrace);
        developer.log(
            '[ERROR] loadMoreMessages(): Raw response that failed to parse',
            name: 'MessageRepository',
            error: {
              'rawResponse': response.body,
            });
        rethrow;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] loadMoreMessages(): ${e.toString()}',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'loadingOlder': older,
          },
          stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Adds a new message to both memory and buffer
  Future<void> addNewMessage(Message message) async {
    try {
      await _ensureInitialized();

      developer.log('[INFO] addNewMessage(): Adding new message',
          name: 'MessageRepository',
          error: {
            'messageId': message.message_id,
            'chatId': message.chat_id,
          });

      _messages.add(message);
      await _bufferManager.addMessage(message);
    } catch (e, stackTrace) {
      developer.log('[ERROR] addNewMessage(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Retrieves all stored chats
  ///
  /// Returns a list of [ChatInfo] objects
  Future<List<ChatInfo>> getAllChats() async {
    try {
      await _ensureInitialized();

      final chats = await _bufferManager.getAllChatsFromBuffer();

      developer.log('[INFO] getAllChats(): Retrieved chats from buffer',
          name: 'MessageRepository', error: {'chatCount': chats.length});

      return chats.map<ChatInfo>((chat) {
        try {
          // If chat is already a ChatInfo object, return it
          if (chat is ChatInfo) {
            return chat;
          }
          // Otherwise try to parse from JSON
          final chatData = chat is Map<String, dynamic>
              ? chat
              : jsonDecode(jsonEncode(chat));
          return ChatInfo.fromJson(chatData);
        } catch (e, stackTrace) {
          // Log both error and stack trace for parsing errors
          developer.log('[ERROR] getAllChats(): Error parsing chat data',
              name: 'MessageRepository', error: e, stackTrace: stackTrace);
          // Log raw data when parsing fails
          developer.log(
              '[ERROR] getAllChats(): Raw chat data that failed to parse',
              name: 'MessageRepository',
              error: {'rawData': chat});
          rethrow;
        }
      }).toList();
    } catch (e, stackTrace) {
      developer.log('[ERROR] getAllChats(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      return [];
    }
  }

  Future<void> loadChats() async {
    try {
      await _ensureInitialized();

      final response = await http.get(
        Uri.parse(AppConstants.newMessaging_getAllChats),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode == 200) {
        // First parse raw message with jsonDecode
        final data = jsonDecode(response.body);
        developer.log('[INFO] loadChats(): Received chats response',
            name: 'MessageRepository', error: {'responseData': data});

        // Then access properties from parsed data
        final List<ChatInfo> chats = data.map<ChatInfo>((json) {
          try {
            return ChatInfo.fromJson(json);
          } catch (e, stackTrace) {
            // Log parsing errors with full context
            developer.log('[ERROR] loadChats(): Error parsing chat data',
                name: 'MessageRepository', error: e, stackTrace: stackTrace);
            developer.log(
                '[ERROR] loadChats(): Raw chat data that failed to parse',
                name: 'MessageRepository',
                error: {'rawData': json});
            rethrow;
          }
        }).toList();

        // Update buffer with parsed chats
        await _bufferManager.addChats(chats);

        developer.log('[INFO] loadChats(): Successfully loaded chats',
            name: 'MessageRepository',
            error: {
              'totalChats': chats.length,
              'activeChats': chats.where((c) => c.is_subscribed).length,
              'requestChats': chats.where((c) => !c.is_subscribed).length
            });
      } else {
        throw Exception('Failed to load chats: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] loadChats(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Updates a chat in the buffer when receiving WebSocket updates
  ///
  /// Handles both new chats and updates to existing chats
  Future<void> updateChatInBuffer(Map<String, dynamic> chatData) async {
    try {
      await _ensureInitialized();

      developer.log(
          '[ENTER] updateChatInBuffer(): Processing WebSocket chat update',
          name: 'MessageRepository',
          error: {'rawData': chatData});

      ChatInfo chat;
      try {
        chat = ChatInfo.fromJson(chatData);
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] updateChatInBuffer(): Failed to parse chat data: ${e.toString()}',
            name: 'MessageRepository',
            stackTrace: stackTrace);
        developer.log(
            '[ERROR] updateChatInBuffer(): Raw chat data that failed to parse',
            name: 'MessageRepository',
            error: {'rawData': chatData});
        rethrow;
      }

      final existingChat = await _bufferManager.getChatInfo(chat.chat_id);

      try {
        if (existingChat != null) {
          // Update existing chat with new values
          existingChat.updateFrom(chat);

          await _bufferManager
              .addChat(existingChat); // Use addChat since it handles updates

          developer.log(
              '[INFO] updateChatInBuffer(): Updated existing chat in buffer',
              name: 'MessageRepository',
              error: {
                'chatId': chat.chat_id,
                'newName': chat.chat_name,
                'newUnreadCount': chat.unread_count,
              });
        } else {
          await _bufferManager.addChat(chat);

          developer.log('[INFO] updateChatInBuffer(): Added new chat to buffer',
              name: 'MessageRepository',
              error: {
                'chatId': chat.chat_id,
                'chatName': chat.chat_name,
              });
        }

        developer.log(
            '[EXIT] updateChatInBuffer(): Successfully processed chat update',
            name: 'MessageRepository');
      } catch (e, stackTrace) {
        developer.log(
            '[ERROR] updateChatInBuffer(): Failed to update/add chat: ${e.toString()}',
            name: 'MessageRepository',
            stackTrace: stackTrace);
        rethrow;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] updateChatInBuffer(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Clears all cached chats and messages
  Future<void> clearCache() async {
    try {
      await _ensureInitialized();

      // Clear the buffer through the buffer manager
      await _bufferManager.clearCache();

      developer.log(
        '[INFO] clearCache(): Cache cleared successfully',
        name: 'MessageRepository',
      );
    } catch (e, stackTrace) {
      developer.log('[ERROR] clearCache(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Checks if a direct chat exists between the current user and another user
  /// Returns the chat info if it exists, null otherwise
  Future<ChatInfo?> checkDirectChat(String otherUserId) async {
    try {
      developer.log('[ENTER] checkDirectChat(): Checking direct chat existence',
          name: 'MessageRepository', error: {'otherUserId': otherUserId});

      final token = AppConstants.appData.newMessagingToken;
      if (token == null || token.isEmpty) {
        throw Exception('No messaging token available');
      }

      final response = await http.get(
        Uri.parse('${AppConstants.newMessaging_checkDirectChat}$otherUserId'),
        headers: {'Authorization': 'Bearer $token'},
      );

      developer.log('[INFO] checkDirectChat(): Got response',
          name: 'MessageRepository',
          error: {'statusCode': response.statusCode, 'body': response.body});

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);
          if (data['chat'] != null) {
            final chatInfo = ChatInfo.fromJson(data['chat']);

            // Cache the chat info
            await bufferManager.addChat(chatInfo);

            developer.log('[EXIT] checkDirectChat(): Found existing chat',
                name: 'MessageRepository',
                error: {
                  'chatId': chatInfo.chat_id,
                  'chatInfo': chatInfo.toJson()
                });

            return chatInfo;
          }
        } catch (e, stackTrace) {
          developer.log('[ERROR] checkDirectChat(): Failed to parse chat data',
              name: 'MessageRepository',
              error: {'error': e.toString(), 'response': response.body},
              stackTrace: stackTrace);
        }
      } else if (response.statusCode != 404) {
        // Log non-404 errors (404 just means no chat exists)
        developer.log('[ERROR] checkDirectChat(): API error',
            name: 'MessageRepository',
            error: {
              'statusCode': response.statusCode,
              'response': response.body
            });
      }

      return null;
    } catch (e, stackTrace) {
      developer.log('[ERROR] checkDirectChat(): ${e.toString()}',
          name: 'MessageRepository',
          error: {'otherUserId': otherUserId},
          stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get chats with pagination and recommendations (V2 API)
  Future<List<ChatInfo>> getChatsV2({
    required String chatType, // "ACTIVE" or "REQUEST"
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] getChatsV2(): Getting chats with pagination',
          name: 'MessageRepository',
          error: {
            'chatType': chatType,
            'limit': limit,
            'offset': offset,
          });

      final queryParams = {
        'chatType': chatType,
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      final uri = Uri.parse(AppConstants.newMessaging_getAllChatsv2)
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}'
        },
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to load chats: ${response.statusCode} - ${response.body}');
      }

      final List<dynamic> data = jsonDecode(response.body);
      final chats =
          data.map((chatData) => ChatInfo.fromJson(chatData)).toList();

      developer.log('[EXIT] getChatsV2(): Successfully retrieved chats',
          name: 'MessageRepository', error: {'chatCount': chats.length});

      return chats;
    } catch (e, stackTrace) {
      developer.log('[ERROR] getChatsV2(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Add members to a chat
  Future<void> addMembersToChat({
    required String chatId,
    required List<String> memberIds,
    String? roleType,
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] addMembersToChat(): Adding members to chat',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'memberIds': memberIds,
            'roleType': roleType,
          });

      final Map<String, dynamic> body = {
        'memberIds': memberIds,
      };

      if (roleType != null) {
        body['role_type'] = roleType;
      }

      final response = await http.post(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/chats/chatId=$chatId/members'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to add members: ${response.statusCode} - ${response.body}');
      }

      developer.log('[EXIT] addMembersToChat(): Successfully added members',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] addMembersToChat(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Remove member from chat
  Future<void> removeMemberFromChat({
    required String chatId,
    required String userId,
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] removeMemberFromChat(): Removing member from chat',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'userId': userId,
          });

      final response = await http.delete(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/chats/chatId-$chatId/members/userId=$userId'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
        },
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to remove member: ${response.statusCode} - ${response.body}');
      }

      developer.log(
          '[EXIT] removeMemberFromChat(): Successfully removed member',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] removeMemberFromChat(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update member role in chat
  Future<void> updateMemberRole({
    required String chatId,
    required String userId,
    required String roleType, // "ADMIN" or "MEMBER"
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] updateMemberRole(): Updating member role',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'userId': userId,
            'roleType': roleType,
          });

      final Map<String, dynamic> body = {
        'role_type': roleType,
      };

      final response = await http.put(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/chats/chatId=$chatId/members/userId=$userId/role'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to update member role: ${response.statusCode} - ${response.body}');
      }

      developer.log(
          '[EXIT] updateMemberRole(): Successfully updated member role',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] updateMemberRole(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update mute status for a chat
  Future<void> updateMuteStatus({
    required String chatId,
    required bool isMuted,
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] updateMuteStatus(): Updating mute status',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'isMuted': isMuted,
          });

      final Map<String, dynamic> body = {
        'is_muted': isMuted,
      };

      final response = await http.put(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/api/chats/chatId=$chatId/mute'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to update mute status: ${response.statusCode} - ${response.body}');
      }

      developer.log(
          '[EXIT] updateMuteStatus(): Successfully updated mute status',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] updateMuteStatus(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get admin group chat IDs
  Future<List<String>> getAdminGroupChatIds() async {
    try {
      await _ensureInitialized();

      developer.log(
          '[ENTER] getAdminGroupChatIds(): Getting admin group chat IDs',
          name: 'MessageRepository');

      final response = await http.get(
        Uri.parse('${AppConstants.newMessaging_baseUrl}/chats/admin-groups'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
        },
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to get admin group chat IDs: ${response.statusCode} - ${response.body}');
      }

      final Map<String, dynamic> data = jsonDecode(response.body);
      final List<String> chatIds = List<String>.from(data['data'] ?? []);

      developer.log(
          '[EXIT] getAdminGroupChatIds(): Successfully retrieved admin group chat IDs',
          name: 'MessageRepository',
          error: {'chatIdCount': chatIds.length});

      return chatIds;
    } catch (e, stackTrace) {
      developer.log('[ERROR] getAdminGroupChatIds(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Create a new chat with message access control
  Future<ChatInfo> createChat({
    required String chatType, // "GROUP" or "DIRECT"
    required String chatName,
    String? messageAccess, // "ADMIN_ONLY", "MEMBERS_ONLY", "ALL", or null
    required List<String> memberIds,
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] createChat(): Creating new chat',
          name: 'MessageRepository',
          error: {
            'chatType': chatType,
            'chatName': chatName,
            'messageAccess': messageAccess,
            'memberIds': memberIds,
          });

      final Map<String, dynamic> body = {
        'chat_type': chatType,
        'chat_name': chatName,
        'member_ids': memberIds,
      };

      if (messageAccess != null) {
        body['message_access'] = messageAccess;
      }

      final response = await http.post(
        Uri.parse('${AppConstants.newMessaging_baseUrl}/chats/create_chat'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to create chat: ${response.statusCode} - ${response.body}');
      }

      final Map<String, dynamic> data = jsonDecode(response.body);
      final ChatInfo chat = ChatInfo.fromJson(data['chat']);

      developer.log('[EXIT] createChat(): Successfully created chat',
          name: 'MessageRepository', error: {'chatId': chat.chat_id});

      return chat;
    } catch (e, stackTrace) {
      developer.log('[ERROR] createChat(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update chat message access
  Future<void> updateChatMessageAccess({
    required String chatId,
    required String
        messageAccess, // "ADMIN_ONLY", "MEMBERS_ONLY", "ALL", or null
  }) async {
    try {
      await _ensureInitialized();

      developer.log(
          '[ENTER] updateChatMessageAccess(): Updating chat message access',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'messageAccess': messageAccess,
          });

      final Map<String, dynamic> body = {
        'messageAccess': messageAccess,
      };

      final response = await http.put(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/api/chats/update_chat/chatId-$chatId'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to update message access: ${response.statusCode} - ${response.body}');
      }

      developer.log(
          '[EXIT] updateChatMessageAccess(): Successfully updated message access',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] updateChatMessageAccess(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update chat name
  Future<void> updateChatName({
    required String chatId,
    required String chatName,
  }) async {
    try {
      await _ensureInitialized();

      developer.log('[ENTER] updateChatName(): Updating chat name',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'chatName': chatName,
          });

      final Map<String, dynamic> body = {
        'chatName': chatName,
      };

      final response = await http.put(
        Uri.parse(
            '${AppConstants.newMessaging_baseUrl}/api/chats/update_chat/chatId-$chatId'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to update chat name: ${response.statusCode} - ${response.body}');
      }

      developer.log('[EXIT] updateChatName(): Successfully updated chat name',
          name: 'MessageRepository');
    } catch (e, stackTrace) {
      developer.log('[ERROR] updateChatName(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }

  ChatBufferManager get bufferManager => _bufferManager;

  /// Mark messages as read via HTTP API (fallback method)
  /// Returns the updated unread count for the user/store
  Future<int?> markMessagesAsRead({
    required String chatId,
    required int lastReadSequence,
  }) async {
    try {
      await _ensureInitialized();

      developer.log(
          '[ENTER] markMessagesAsRead(): Marking messages as read via HTTP API',
          name: 'MessageRepository',
          error: {
            'chatId': chatId,
            'lastReadSequence': lastReadSequence,
          });

      final Map<String, dynamic> body = {
        'chat_id': chatId,
        'last_read_sequence': lastReadSequence,
      };

      final response = await http.post(
        Uri.parse(AppConstants.newMessaging_markAsRead),
        headers: {
          'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to mark messages as read: ${response.statusCode} - ${response.body}');
      }

      // Parse response and extract unread count if available
      try {
        final responseData = jsonDecode(response.body);
        final int? unreadCount = responseData['unreadCount'];

        developer.log(
            '[INFO] markMessagesAsRead(): Messages marked as read successfully',
            name: 'MessageRepository',
            error: {'unreadCount': unreadCount, 'response': responseData});

        return unreadCount;
      } catch (e) {
        developer.log(
            '[WARNING] markMessagesAsRead(): Could not parse unread count from response',
            name: 'MessageRepository',
            error: {'error': e.toString(), 'response': response.body});
        return null;
      }
    } catch (e, stackTrace) {
      developer.log('[ERROR] markMessagesAsRead(): ${e.toString()}',
          name: 'MessageRepository', stackTrace: stackTrace);
      rethrow;
    }
  }
}
