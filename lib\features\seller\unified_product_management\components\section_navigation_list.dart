import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_form_bloc.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

/// Section navigation list component
class SectionNavigationList extends StatelessWidget {
  final UnifiedProductFormBloc formBloc;
  final Map<String, bool> sectionCompletion;
  final Function(ProductFormSectionType) onSectionTap;

  const SectionNavigationList({
    Key? key,
    required this.formBloc,
    required this.sectionCompletion,
    required this.onSectionTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final sections = ProductFormSectionType.values;
    sections.sort((a, b) => a.order.compareTo(b.order));

    return Column(
      children: sections.map((sectionType) {
        return _buildSectionTile(context, sectionType);
      }).toList(),
    );
  }

  Widget _buildSectionTile(BuildContext context, ProductFormSectionType sectionType) {
    final isCompleted = sectionCompletion[sectionType.id] ?? false;
    final sectionData = formBloc.getSectionData(sectionType);
    final hasData = _sectionHasData(sectionType, sectionData);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onSectionTap(sectionType),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isCompleted 
                    ? AppColors.brandBlack.withOpacity(0.3)
                    : AppColors.borderColor1,
                width: isCompleted ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                _buildSectionIcon(sectionType, isCompleted),
                horizontalSizedBox(16),
                Expanded(
                  child: _buildSectionContent(sectionType, isCompleted, hasData),
                ),
                horizontalSizedBox(8),
                _buildSectionStatus(sectionType, isCompleted, hasData),
                horizontalSizedBox(8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.writingColor2,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionIcon(ProductFormSectionType sectionType, bool isCompleted) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: isCompleted 
            ? AppColors.brandBlack 
            : AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        sectionType.icon,
        color: isCompleted 
            ? AppColors.appWhite 
            : AppColors.writingColor2,
        size: 24,
      ),
    );
  }

  Widget _buildSectionContent(
    ProductFormSectionType sectionType, 
    bool isCompleted, 
    bool hasData
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              sectionType.displayName,
              style: AppTextStyle.contentText0(
                textColor: AppColors.appBlack,
              ).copyWith(
                fontWeight: isCompleted ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            if (sectionType.isRequired) ...[
              horizontalSizedBox(4),
              Text(
                '*',
                style: AppTextStyle.contentText0(textColor: Colors.red),
              ),
            ],
          ],
        ),
        verticalSizedBox(4),
        Text(
          sectionType.description,
          style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (hasData && !isCompleted) ...[
          verticalSizedBox(4),
          _buildDataPreview(sectionType),
        ],
      ],
    );
  }

  Widget _buildSectionStatus(
    ProductFormSectionType sectionType, 
    bool isCompleted, 
    bool hasData
  ) {
    if (isCompleted) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check,
              size: 12,
              color: AppColors.appWhite,
            ),
            horizontalSizedBox(4),
            Text(
              'Complete',
              style: AppTextStyle.smallText(textColor: AppColors.appWhite),
            ),
          ],
        ),
      );
    }

    if (hasData) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.edit,
              size: 12,
              color: Colors.orange,
            ),
            horizontalSizedBox(4),
            Text(
              'In Progress',
              style: AppTextStyle.smallText(textColor: Colors.orange),
            ),
          ],
        ),
      );
    }

    if (sectionType.isRequired) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          'Required',
          style: AppTextStyle.smallText(textColor: Colors.red),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        'Optional',
        style: AppTextStyle.smallText(textColor: AppColors.writingColor2),
      ),
    );
  }

  Widget _buildDataPreview(ProductFormSectionType sectionType) {
    final sectionData = formBloc.getSectionData(sectionType);
    String previewText = '';

    switch (sectionType) {
      case ProductFormSectionType.basicDetails:
        final productName = sectionData['productName']?.toString() ?? '';
        final brandName = sectionData['brandName']?.toString() ?? '';
        if (productName.isNotEmpty || brandName.isNotEmpty) {
          previewText = [productName, brandName]
              .where((s) => s.isNotEmpty)
              .join(' • ');
        }
        break;
      
      case ProductFormSectionType.inventory:
        final hasOptions = sectionData['hasOptions'] ?? false;
        final variantCount = (sectionData['variants'] as List?)?.length ?? 0;
        if (hasOptions && variantCount > 0) {
          previewText = '$variantCount variants configured';
        } else {
          previewText = 'Basic inventory setup';
        }
        break;
      
      case ProductFormSectionType.swadeshiLabels:
        final labels = [
          sectionData['swadeshiBrand'],
          sectionData['swadeshiMade'],
          sectionData['swadeshiOwned'],
        ].where((label) => label != null).length;
        if (labels > 0) {
          previewText = '$labels of 3 labels set';
        }
        break;
      
      case ProductFormSectionType.fulfillmentSettings:
        if (sectionData.isNotEmpty) {
          previewText = 'Delivery settings configured';
        }
        break;
      
      case ProductFormSectionType.returnSettings:
        if (sectionData.isNotEmpty) {
          previewText = 'Return policy configured';
        }
        break;
      
      case ProductFormSectionType.productPromotions:
        final isEnabled = sectionData['isAffiliatePromotionEnabled'] ?? false;
        if (isEnabled) {
          final commission = sectionData['affiliateCommissionAmount'] ?? 0.0;
          previewText = 'Affiliate promotion: ${commission}%';
        }
        break;
      
      case ProductFormSectionType.visibility:
        final fields = [
          sectionData['productSlug'],
          sectionData['productCode'],
          sectionData['productTags'],
          sectionData['targetGender'],
        ].where((field) => field?.toString().isNotEmpty == true).length;
        if (fields > 0) {
          previewText = '$fields visibility settings configured';
        }
        break;
      
      case ProductFormSectionType.moreDetails:
        if (sectionData.isNotEmpty) {
          previewText = 'Additional details added';
        }
        break;
    }

    if (previewText.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: AppColors.brandBlack.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        previewText,
        style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  bool _sectionHasData(ProductFormSectionType sectionType, Map<String, dynamic> sectionData) {
    switch (sectionType) {
      case ProductFormSectionType.basicDetails:
        return sectionData.values.any((value) => 
            value?.toString().trim().isNotEmpty == true);
      
      case ProductFormSectionType.inventory:
        return true; // Always has default data
      
      case ProductFormSectionType.swadeshiLabels:
        return sectionData.values.any((value) => value != null);
      
      case ProductFormSectionType.fulfillmentSettings:
      case ProductFormSectionType.returnSettings:
      case ProductFormSectionType.moreDetails:
        return sectionData.isNotEmpty;
      
      case ProductFormSectionType.productPromotions:
        return sectionData['isAffiliatePromotionEnabled'] == true ||
               sectionData['promotionLink']?.toString().isNotEmpty == true;
      
      case ProductFormSectionType.visibility:
        return sectionData.values.any((value) => 
            value?.toString().trim().isNotEmpty == true);
    }
  }
}
