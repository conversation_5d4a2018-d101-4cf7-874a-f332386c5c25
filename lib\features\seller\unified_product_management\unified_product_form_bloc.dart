import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/model/unified_product_form_data/unified_product_form_data.dart';
import 'package:swadesic/model/unified_product_image_state/unified_product_image_state.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

/// States for the unified product form
enum UnifiedProductFormState {
  loading,
  loaded,
  saving,
  saved,
  error,
}

/// Main bloc for unified product management
class UnifiedProductFormBloc {
  final BuildContext context;
  late UnifiedProductFormData _formData;
  
  // Services
  late ProductAndImageServices _productAndImageServices;
  
  // Stream controllers
  final _formStateController = StreamController<UnifiedProductFormState>.broadcast();
  final _formDataController = StreamController<UnifiedProductFormData>.broadcast();
  final _sectionCompletionController = StreamController<Map<String, bool>>.broadcast();
  final _validationController = StreamController<FormValidationResult>.broadcast();
  final _imageStateController = StreamController<UnifiedProductImageState>.broadcast();
  
  // Streams
  Stream<UnifiedProductFormState> get formStateStream => _formStateController.stream;
  Stream<UnifiedProductFormData> get formDataStream => _formDataController.stream;
  Stream<Map<String, bool>> get sectionCompletionStream => _sectionCompletionController.stream;
  Stream<FormValidationResult> get validationStream => _validationController.stream;
  Stream<UnifiedProductImageState> get imageStateStream => _imageStateController.stream;
  
  // Current state
  UnifiedProductFormState _currentState = UnifiedProductFormState.loading;
  FormValidationResult? _lastValidationResult;

  UnifiedProductFormBloc({
    required this.context,
    required int storeId,
    required String storeReference,
    String? productReference, // null for add mode, provided for edit mode
  }) {
    _productAndImageServices = ProductAndImageServices();
    
    // Initialize form data based on mode
    if (productReference != null) {
      // Edit mode
      _formData = UnifiedProductFormData.forEdit(
        productReference: productReference,
        storeId: storeId,
        storeReference: storeReference,
      );
    } else {
      // Add mode
      _formData = UnifiedProductFormData.forAdd(
        storeId: storeId,
        storeReference: storeReference,
      );
    }
  }

  // Getters
  UnifiedProductFormData get formData => _formData;
  UnifiedProductFormState get currentState => _currentState;
  bool get isEditMode => _formData.isEditMode;
  bool get isAddMode => _formData.isAddMode;
  bool get hasUnsavedChanges => _formData.imageState.hasUnsavedChanges || _hasFormChanges();
  FormValidationResult? get lastValidationResult => _lastValidationResult;

  // Initialize the form
  Future<void> initialize() async {
    try {
      _updateState(UnifiedProductFormState.loading);
      
      if (isEditMode) {
        await _loadExistingProductData();
        await _initializeEditMode();
      } else {
        await _initializeAddMode();
        await _loadStoreDefaults();
      }
      
      _updateState(UnifiedProductFormState.loaded);
      _broadcastFormData();
      _broadcastSectionCompletion();
      
    } catch (e) {
      _updateState(UnifiedProductFormState.error);
      if (context.mounted) {
        CommonMethods.toastMessage(
          'Failed to initialize form: ${e.toString()}',
          context,
        );
      }
    }
  }

  // Load existing product data for edit mode
  Future<void> _loadExistingProductData() async {
    if (_formData.productReference == null) return;
    
    try {
      // Get product details
      final productResponse = await _productAndImageServices
          .getOnlyProduct(_formData.productReference!);
      
      if (productResponse.singleProductData != null) {
        _formData.existingProduct = productResponse.singleProductData!;
        
        // Get product images
        final imageResponse = await _productAndImageServices
            .getOnlyProductImage(_formData.productReference!);
        
        // Initialize image state with existing images
        _formData.imageState = UnifiedProductImageState.forEdit(
          imageResponse.data ?? [],
        );
        
        // Populate form sections with existing data
        _populateFormFromExistingProduct();
      }
    } catch (e) {
      throw Exception('Failed to load existing product data: $e');
    }
  }

  // Populate form sections from existing product
  void _populateFormFromExistingProduct() {
    final product = _formData.existingProduct!;
    
    // Basic details
    _formData.updateBasicDetails({
      'productName': product.productName ?? '',
      'brandName': product.brandName ?? '',
      'productCategory': product.productCategory ?? '',
      'productDescription': product.productDescription ?? '',
    });
    
    // Inventory (Note: MRP, selling price, stock are deprecated in API)
    _formData.updateInventory({
      'hasOptions': product.options != null && product.options!.isNotEmpty,
      'options': product.options ?? {},
      'variants': product.variants?.map((v) => v).toList() ?? [],
      'hasMultipleOptions': product.options != null && product.options!.isNotEmpty,
    });
    
    // Swadeshi labels
    _formData.updateSwadeshiLabels({
      'swadeshiBrand': product.swadeshiBrand,
      'swadeshiMade': product.swadeshiMade,
      'swadeshiOwned': product.swadeshiOwned,
    });
    
    // Promotions
    _formData.updatePromotions({
      'isAffiliatePromotionEnabled': product.isPromotionEnabled ?? false,
      'affiliateCommissionAmount': product.promotionAmount ?? 0.0,
      'promotionLink': product.promotionLink ?? '',
    });
    
    // Visibility
    _formData.updateVisibility({
      'productSlug': product.productSlug ?? '',
      'productCode': product.productCode ?? '',
      'productTags': product.hashTag ?? '',
      'targetGender': product.targetGender ?? '',
    });
    
    // Mark as having auto-filled data
    _formData.hasAutoFilledData = true;
  }

  // Initialize edit mode
  Future<void> _initializeEditMode() async {
    // Load any additional edit-specific data
    await _loadDeliveryAndReturnSettings();
  }

  // Initialize add mode
  Future<void> _initializeAddMode() async {
    // Auto-fill brand name with store handle if available
    final storeDashboard = Provider.of<StoreDashboardDataModel>(context, listen: false);
    if (storeDashboard.storeDashBoard.storehandle?.isNotEmpty == true) {
      _formData.updateBasicDetails({
        'brandName': storeDashboard.storeDashBoard.storehandle!,
      });
    }
  }

  // Load store defaults
  Future<void> _loadStoreDefaults() async {
    try {
      // Load store-level delivery and return settings as defaults
      await _loadDeliveryAndReturnSettings();
    } catch (e) {
      // Non-critical error, continue without defaults
      debugPrint('Failed to load store defaults: $e');
    }
  }

  // Load delivery and return settings
  Future<void> _loadDeliveryAndReturnSettings() async {
    // This will be implemented when integrating with delivery/return settings
    // For now, mark as placeholder
    _formData.storeDefaults['deliverySettings'] = {};
    _formData.storeDefaults['returnSettings'] = {};
  }

  // Update section data
  void updateSectionData(ProductFormSectionType sectionType, Map<String, dynamic> data) {
    switch (sectionType) {
      case ProductFormSectionType.basicDetails:
        _formData.updateBasicDetails(data);
        break;
      case ProductFormSectionType.inventory:
        _formData.updateInventory(data);
        break;
      case ProductFormSectionType.swadeshiLabels:
        _formData.updateSwadeshiLabels(data);
        break;
      case ProductFormSectionType.fulfillmentSettings:
        _formData.updateFulfillmentSettings(data);
        break;
      case ProductFormSectionType.returnSettings:
        _formData.updateReturnSettings(data);
        break;
      case ProductFormSectionType.productPromotions:
        _formData.updatePromotions(data);
        break;
      case ProductFormSectionType.visibility:
        _formData.updateVisibility(data);
        break;
      case ProductFormSectionType.moreDetails:
        _formData.updateMoreDetails(data);
        break;
    }
    
    _broadcastFormData();
    _broadcastSectionCompletion();
    _validateForm();
  }

  // Get section data
  Map<String, dynamic> getSectionData(ProductFormSectionType sectionType) {
    switch (sectionType) {
      case ProductFormSectionType.basicDetails:
        return _formData.basicDetails;
      case ProductFormSectionType.inventory:
        return _formData.inventory;
      case ProductFormSectionType.swadeshiLabels:
        return _formData.swadeshiLabels;
      case ProductFormSectionType.fulfillmentSettings:
        return _formData.fulfillmentSettings;
      case ProductFormSectionType.returnSettings:
        return _formData.returnSettings;
      case ProductFormSectionType.productPromotions:
        return _formData.promotions;
      case ProductFormSectionType.visibility:
        return _formData.visibility;
      case ProductFormSectionType.moreDetails:
        return _formData.moreDetails;
    }
  }

  // Reset section to defaults
  void resetSection(ProductFormSectionType sectionType) {
    _formData.resetSection(sectionType.id);
    _broadcastFormData();
    _broadcastSectionCompletion();
    _validateForm();
  }

  // Update image state
  void updateImageState(UnifiedProductImageState newImageState) {
    _formData.imageState = newImageState;
    _imageStateController.add(newImageState);
    _broadcastFormData();
  }

  // Validate form
  FormValidationResult validateForm() {
    final errors = <ProductFormSectionType, List<String>>{};
    final globalErrors = <String>[];
    
    // Validate basic details
    if (_formData.basicDetails['productName']?.toString().trim().isEmpty == true) {
      errors[ProductFormSectionType.basicDetails] = ['Product name is required'];
    }
    
    // Validate swadeshi labels
    if (_formData.swadeshiLabels['swadeshiBrand'] == null ||
        _formData.swadeshiLabels['swadeshiMade'] == null ||
        _formData.swadeshiLabels['swadeshiOwned'] == null) {
      errors[ProductFormSectionType.swadeshiLabels] = ['All Swadeshi labels are required'];
    }
    
    // Validate images
    if (!_formData.imageState.hasImages) {
      globalErrors.add('At least one product image is required');
    }
    
    // Validate delivery or return settings
    if (_formData.fulfillmentSettings.isEmpty && _formData.returnSettings.isEmpty) {
      globalErrors.add('Either delivery settings or return settings must be configured');
    }
    
    final result = FormValidationResult(
      isValid: errors.isEmpty && globalErrors.isEmpty,
      sectionErrors: errors,
      globalErrors: globalErrors,
    );
    
    _lastValidationResult = result;
    _validationController.add(result);
    
    return result;
  }

  // Private validation method
  void _validateForm() {
    validateForm();
  }

  // Check if form has changes
  bool _hasFormChanges() {
    // This would compare current form data with original data
    // For now, return true if any section has data
    return _formData.basicDetails.values.any((value) => value.toString().isNotEmpty) ||
           _formData.swadeshiLabels.values.any((value) => value != null) ||
           _formData.fulfillmentSettings.isNotEmpty ||
           _formData.returnSettings.isNotEmpty;
  }

  // Update state
  void _updateState(UnifiedProductFormState newState) {
    _currentState = newState;
    _formStateController.add(newState);
  }

  // Broadcast form data
  void _broadcastFormData() {
    _formDataController.add(_formData);
  }

  // Broadcast section completion
  void _broadcastSectionCompletion() {
    _sectionCompletionController.add(_formData.sectionCompletion);
  }

  // Dispose
  void dispose() {
    _formStateController.close();
    _formDataController.close();
    _sectionCompletionController.close();
    _validationController.close();
    _imageStateController.close();
  }
}
