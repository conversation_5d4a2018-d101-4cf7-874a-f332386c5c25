import 'dart:async';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_profile/user_profile_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum PostalCodeState { Loading, Success, Failed }

class UpdatePinCodeUtil extends StatefulWidget {
  final BuildContext context;

  const UpdatePinCodeUtil({Key? key, required this.context}) : super(key: key);

  @override
  State<UpdatePinCodeUtil> createState() => _UpdatePinCodeUtilState();
}

class _UpdatePinCodeUtilState extends State<UpdatePinCodeUtil> {
  final TextEditingController postalCodeTextCtrl = TextEditingController();
  late UserProfileService userProfileService;
  late StreamController<PostalCodeState> postalCtrl;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    userProfileService = UserProfileService();
    postalCtrl = StreamController<PostalCodeState>.broadcast();
    // Initialize with current pincode
    postalCodeTextCtrl.text = AppConstants.appData.pinCode ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 32, vertical: 34),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(AppImages.locationIcon, height: 48, width: 48),
          Container(
            margin: const EdgeInsets.only(top: 23, bottom: 20),
            child: Text(
              AppStrings.deliveryPinCode,
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 80),
            child: AppTextFields.onlyNumberTextField(
              context: context,
              textAlign: TextAlign.center,
              maxEntry: 6,
              textEditingController: postalCodeTextCtrl,
              hintText: AppStrings.pinCode,
            ),
          ),
          verticalSizedBox(20),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Visibility(
                visible: !CommonMethods.isWeb(),
                child: Container(
                  margin: const EdgeInsets.only(right: 18),
                  child: StreamBuilder<PostalCodeState>(
                    stream: postalCtrl.stream,
                    initialData: PostalCodeState.Success,
                    builder: (context, snapshot) {
                      if (snapshot.data == PostalCodeState.Success) {
                        return InkWell(
                          onTap: () {
                            // GPS functionality - simplified version
                            CommonMethods.toastMessage("GPS feature coming soon", context, commonErrorMessage: true);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(Radius.circular(80)),
                              color: AppColors.textFieldFill1,
                            ),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(AppImages.location2, height: 20, width: 20),
                                  horizontalSizedBox(10),
                                  Text(
                                    AppStrings.useGps,
                                    style: AppTextStyle.access0(textColor: AppColors.appBlack),
                                  )
                                ],
                              ),
                            ),
                          ),
                        );
                      }
                      return const CircularProgressIndicator();
                    },
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  sendPinCode();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(80)),
                    color: AppColors.appBlack,
                  ),
                  child: Center(
                    child: Row(
                      children: [
                        isLoading
                            ? SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.appWhite,
                                ),
                              )
                            : Text(
                                AppStrings.save,
                                style: AppTextStyle.access0(textColor: AppColors.appWhite),
                              )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
  //endregion

  //region Send PinCode
  void sendPinCode() async {
    if (isLoading) return;

    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);

    try {
      setState(() {
        isLoading = true;
      });

      bool pinValidation =
          CommonMethods().pinCodeValidation(postalCodeTextCtrl.text);
      if (!pinValidation) {
        CommonMethods.toastMessage("Invalid postal code", context);
        setState(() {
          isLoading = false;
        });
        return;
      }

      // If user is signed in, update pincode in backend
      if (!CommonMethods().isStaticUser()) {
        await userProfileService.addUserPinCode(postalCodeTextCtrl.text);
        //Update pincode in app constant
        AppConstants.appData.pinCode = postalCodeTextCtrl.text;
      } else {
        // For unsigned users, just update the pincode locally
        AppConstants.appData.pinCode = postalCodeTextCtrl.text;
      }

      //Immediately update cache storage
      AppDataService().addAppData();
      Navigator.pop(context);
      CommonMethods.toastMessage(AppStrings.deliveryPinCodeUpdated, context);

      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
  //endregion

  @override
  void dispose() {
    postalCodeTextCtrl.dispose();
    postalCtrl.close();
    super.dispose();
  }
}
