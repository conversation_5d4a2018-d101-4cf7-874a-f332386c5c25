import 'package:image_picker/image_picker.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';

class ProductImageState {
  List<ProdImages> existingImages;
  List<XFile> pendingImages;
  List<int> deletedImageIds;
  List<Map<String, dynamic>> orderedStructure;
  List<Map<String, dynamic>>? existingOrderStructure; // Add this field
  bool hasUnsavedChanges;

  ProductImageState({
    required this.existingImages,
    this.pendingImages = const [],
    this.deletedImageIds = const [],
    this.orderedStructure = const [],
    this.existingOrderStructure, // Add to constructor
    this.hasUnsavedChanges = false,
  });

  ProductImageState copyWith({
    List<ProdImages>? existingImages,
    List<XFile>? pendingImages,
    List<int>? deletedImageIds,
    List<Map<String, dynamic>>? orderedStructure,
    List<Map<String, dynamic>>? existingOrderStructure, // Add to copyWith
    bool? hasUnsavedChanges,
  }) {
    return ProductImageState(
      existingImages: existingImages ?? this.existingImages,
      pendingImages: pendingImages ?? this.pendingImages,
      deletedImageIds: deletedImageIds ?? this.deletedImageIds,
      orderedStructure: orderedStructure ?? this.orderedStructure,
      existingOrderStructure: existingOrderStructure ??
          this.existingOrderStructure, // Add to return
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
    );
  }
}
