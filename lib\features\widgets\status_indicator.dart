import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';

class StatusIndicator extends StatefulWidget {
  final bool isActive;
  final double size;
  final String label;
  final Color? activeColor;
  final Color? inactiveColor;
  final Widget? cta_page;

  const StatusIndicator({
    Key? key,
    required this.isActive,
    this.size = 16.0,
    required this.label,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.cta_page,
  }) : super(key: key);

  @override
  _StatusIndicatorState createState() => _StatusIndicatorState();
}

class _StatusIndicatorState extends State<StatusIndicator> {
  bool _isTapped = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isTapped = !_isTapped;
        });
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.isActive
                  ? widget.activeColor!.withOpacity(0.2)
                  : widget.inactiveColor!.withOpacity(0.2),
            ),
            child: Center(
              child: Container(
                width: widget.size * 0.6,
                height: widget.size * 0.6,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.isActive
                      ? widget.activeColor
                      : widget.inactiveColor,
                  border: Border.all(
                    color: widget.isActive
                        ? widget.activeColor!
                        : widget.inactiveColor!,
                    width: 1.5,
                  ),
                ),
              ),
            ),
          ),
          if (_isTapped) const SizedBox(width: 4),
          if (_isTapped)
            Text(
              widget.label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.appBlack,
              ),
            ),
        ],
      ),
    );
  }
}
