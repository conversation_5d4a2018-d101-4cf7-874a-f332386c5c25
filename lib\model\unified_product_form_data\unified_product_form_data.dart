import 'package:image_picker/image_picker.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_image_state/product_image_state.dart';
import 'package:swadesic/model/product_option/product_option.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

/// Unified data model for managing both add and edit product flows
class UnifiedProductFormData {
  // Mode detection
  final bool isEditMode;
  final String? productReference;
  final int? storeId;
  final String storeReference;
  
  // Existing product data (for edit mode)
  Product? existingProduct;
  
  // Form sections data
  Map<String, dynamic> basicDetails;
  Map<String, dynamic> inventory;
  Map<String, dynamic> swadeshiLabels;
  Map<String, dynamic> fulfillmentSettings;
  Map<String, dynamic> returnSettings;
  Map<String, dynamic> promotions;
  Map<String, dynamic> visibility;
  Map<String, dynamic> moreDetails;
  
  // Image management
  UnifiedProductImageState imageState;
  
  // Section completion tracking
  Map<String, bool> sectionCompletion;
  
  // Validation state
  Map<String, List<String>> validationErrors;
  
  // Auto-fill and defaults
  bool hasAutoFilledData;
  Map<String, dynamic> storeDefaults;

  UnifiedProductFormData({
    required this.isEditMode,
    this.productReference,
    required this.storeId,
    required this.storeReference,
    this.existingProduct,
    Map<String, dynamic>? basicDetails,
    Map<String, dynamic>? inventory,
    Map<String, dynamic>? swadeshiLabels,
    Map<String, dynamic>? fulfillmentSettings,
    Map<String, dynamic>? returnSettings,
    Map<String, dynamic>? promotions,
    Map<String, dynamic>? visibility,
    Map<String, dynamic>? moreDetails,
    UnifiedProductImageState? imageState,
    Map<String, bool>? sectionCompletion,
    Map<String, List<String>>? validationErrors,
    this.hasAutoFilledData = false,
    Map<String, dynamic>? storeDefaults,
  }) : 
    basicDetails = basicDetails ?? _getDefaultBasicDetails(),
    inventory = inventory ?? _getDefaultInventory(),
    swadeshiLabels = swadeshiLabels ?? _getDefaultSwadeshiLabels(),
    fulfillmentSettings = fulfillmentSettings ?? {},
    returnSettings = returnSettings ?? {},
    promotions = promotions ?? _getDefaultPromotions(),
    visibility = visibility ?? _getDefaultVisibility(),
    moreDetails = moreDetails ?? {},
    imageState = imageState ?? UnifiedProductImageState(),
    sectionCompletion = sectionCompletion ?? _getDefaultSectionCompletion(),
    validationErrors = validationErrors ?? {},
    storeDefaults = storeDefaults ?? {};

  // Factory constructor for add mode
  factory UnifiedProductFormData.forAdd({
    required int storeId,
    required String storeReference,
  }) {
    return UnifiedProductFormData(
      isEditMode: false,
      storeId: storeId,
      storeReference: storeReference,
    );
  }

  // Factory constructor for edit mode
  factory UnifiedProductFormData.forEdit({
    required String productReference,
    required int storeId,
    required String storeReference,
    Product? existingProduct,
  }) {
    return UnifiedProductFormData(
      isEditMode: true,
      productReference: productReference,
      storeId: storeId,
      storeReference: storeReference,
      existingProduct: existingProduct,
    );
  }

  // Default data generators
  static Map<String, dynamic> _getDefaultBasicDetails() {
    return {
      'productName': '',
      'brandName': '',
      'productCategory': '',
      'productDescription': '',
    };
  }

  static Map<String, dynamic> _getDefaultInventory() {
    return {
      'hasOptions': false,
      'options': <String, List<String>>{},
      'variants': <ProductVariant>[],
      'hasMultipleOptions': false,
    };
  }

  static Map<String, dynamic> _getDefaultSwadeshiLabels() {
    return {
      'swadeshiBrand': null,
      'swadeshiMade': null,
      'swadeshiOwned': null,
    };
  }

  static Map<String, dynamic> _getDefaultPromotions() {
    return {
      'isAffiliatePromotionEnabled': false,
      'affiliateCommissionAmount': 0.0,
      'promotionLink': '',
    };
  }

  static Map<String, dynamic> _getDefaultVisibility() {
    return {
      'productSlug': '',
      'productCode': '',
      'productTags': '',
      'targetGender': '',
    };
  }

  static Map<String, bool> _getDefaultSectionCompletion() {
    return {
      'basicDetails': false,
      'inventory': false,
      'swadeshiLabels': false,
      'fulfillmentSettings': false,
      'returnSettings': false,
      'promotions': false,
      'visibility': false,
      'moreDetails': false,
    };
  }

  // Helper methods
  bool get isAddMode => !isEditMode;
  
  bool get isFormValid => validationErrors.isEmpty;
  
  bool get hasRequiredSections {
    return sectionCompletion['basicDetails'] == true &&
           sectionCompletion['swadeshiLabels'] == true &&
           (sectionCompletion['fulfillmentSettings'] == true || 
            sectionCompletion['returnSettings'] == true);
  }

  int get completedSectionsCount {
    return sectionCompletion.values.where((completed) => completed).length;
  }

  int get totalSectionsCount => sectionCompletion.length;

  double get completionPercentage {
    if (totalSectionsCount == 0) return 0.0;
    return completedSectionsCount / totalSectionsCount;
  }

  // Update methods
  void updateBasicDetails(Map<String, dynamic> data) {
    basicDetails.addAll(data);
    _checkBasicDetailsCompletion();
  }

  void updateInventory(Map<String, dynamic> data) {
    inventory.addAll(data);
    _checkInventoryCompletion();
  }

  void updateSwadeshiLabels(Map<String, dynamic> data) {
    swadeshiLabels.addAll(data);
    _checkSwadeshiLabelsCompletion();
  }

  void updateFulfillmentSettings(Map<String, dynamic> data) {
    fulfillmentSettings.addAll(data);
    _checkFulfillmentCompletion();
  }

  void updateReturnSettings(Map<String, dynamic> data) {
    returnSettings.addAll(data);
    _checkReturnSettingsCompletion();
  }

  void updatePromotions(Map<String, dynamic> data) {
    promotions.addAll(data);
    _checkPromotionsCompletion();
  }

  void updateVisibility(Map<String, dynamic> data) {
    visibility.addAll(data);
    _checkVisibilityCompletion();
  }

  void updateMoreDetails(Map<String, dynamic> data) {
    moreDetails.addAll(data);
    _checkMoreDetailsCompletion();
  }

  // Section completion checkers
  void _checkBasicDetailsCompletion() {
    sectionCompletion['basicDetails'] = 
      basicDetails['productName']?.toString().trim().isNotEmpty == true &&
      basicDetails['brandName']?.toString().trim().isNotEmpty == true &&
      basicDetails['productCategory']?.toString().trim().isNotEmpty == true &&
      basicDetails['productDescription']?.toString().trim().isNotEmpty == true;
  }

  void _checkInventoryCompletion() {
    // Inventory is always considered complete since it has defaults
    // The actual validation happens in the inventory options screen
    sectionCompletion['inventory'] = true;
  }

  void _checkSwadeshiLabelsCompletion() {
    sectionCompletion['swadeshiLabels'] = 
      swadeshiLabels['swadeshiBrand'] != null &&
      swadeshiLabels['swadeshiMade'] != null &&
      swadeshiLabels['swadeshiOwned'] != null;
  }

  void _checkFulfillmentCompletion() {
    sectionCompletion['fulfillmentSettings'] = 
      fulfillmentSettings.isNotEmpty;
  }

  void _checkReturnSettingsCompletion() {
    sectionCompletion['returnSettings'] = 
      returnSettings.isNotEmpty;
  }

  void _checkPromotionsCompletion() {
    sectionCompletion['promotions'] = true; // Optional section
  }

  void _checkVisibilityCompletion() {
    sectionCompletion['visibility'] = true; // Optional section
  }

  void _checkMoreDetailsCompletion() {
    sectionCompletion['moreDetails'] = true; // Optional section
  }

  // Reset methods
  void resetSection(String sectionName) {
    switch (sectionName) {
      case 'basicDetails':
        basicDetails = _getDefaultBasicDetails();
        break;
      case 'inventory':
        inventory = _getDefaultInventory();
        break;
      case 'swadeshiLabels':
        swadeshiLabels = _getDefaultSwadeshiLabels();
        break;
      case 'fulfillmentSettings':
        fulfillmentSettings.clear();
        break;
      case 'returnSettings':
        returnSettings.clear();
        break;
      case 'promotions':
        promotions = _getDefaultPromotions();
        break;
      case 'visibility':
        visibility = _getDefaultVisibility();
        break;
      case 'moreDetails':
        moreDetails.clear();
        break;
    }
    sectionCompletion[sectionName] = false;
    validationErrors.remove(sectionName);
  }

  // Convert to Product object for API calls
  Product toProduct() {
    return Product(
      productReference: productReference ?? "New",
      productName: basicDetails['productName'],
      brandName: basicDetails['brandName'],
      productCategory: basicDetails['productCategory'],
      productDescription: basicDetails['productDescription'],
      promotionLink: promotions['promotionLink'],
      hashTag: visibility['productTags'],
      targetGender: visibility['targetGender'],
      isPromotionEnabled: promotions['isAffiliatePromotionEnabled'],
      promotionAmount: promotions['affiliateCommissionAmount'],
      productSlug: visibility['productSlug'],
      productCode: visibility['productCode'],
      swadeshiBrand: swadeshiLabels['swadeshiBrand'],
      swadeshiMade: swadeshiLabels['swadeshiMade'],
      swadeshiOwned: swadeshiLabels['swadeshiOwned'],
      storeReference: storeReference,
      storeid: storeId,
      options: inventory['options'],
      variants: inventory['variants']?.map((v) => v is ProductVariant ? v.toJson() : v).toList(),
      prodImages: imageState.existingImages,
    );
  }

  // Copy with method
  UnifiedProductFormData copyWith({
    bool? isEditMode,
    String? productReference,
    int? storeId,
    String? storeReference,
    Product? existingProduct,
    Map<String, dynamic>? basicDetails,
    Map<String, dynamic>? inventory,
    Map<String, dynamic>? swadeshiLabels,
    Map<String, dynamic>? fulfillmentSettings,
    Map<String, dynamic>? returnSettings,
    Map<String, dynamic>? promotions,
    Map<String, dynamic>? visibility,
    Map<String, dynamic>? moreDetails,
    UnifiedProductImageState? imageState,
    Map<String, bool>? sectionCompletion,
    Map<String, List<String>>? validationErrors,
    bool? hasAutoFilledData,
    Map<String, dynamic>? storeDefaults,
  }) {
    return UnifiedProductFormData(
      isEditMode: isEditMode ?? this.isEditMode,
      productReference: productReference ?? this.productReference,
      storeId: storeId ?? this.storeId,
      storeReference: storeReference ?? this.storeReference,
      existingProduct: existingProduct ?? this.existingProduct,
      basicDetails: basicDetails ?? Map.from(this.basicDetails),
      inventory: inventory ?? Map.from(this.inventory),
      swadeshiLabels: swadeshiLabels ?? Map.from(this.swadeshiLabels),
      fulfillmentSettings: fulfillmentSettings ?? Map.from(this.fulfillmentSettings),
      returnSettings: returnSettings ?? Map.from(this.returnSettings),
      promotions: promotions ?? Map.from(this.promotions),
      visibility: visibility ?? Map.from(this.visibility),
      moreDetails: moreDetails ?? Map.from(this.moreDetails),
      imageState: imageState ?? this.imageState,
      sectionCompletion: sectionCompletion ?? Map.from(this.sectionCompletion),
      validationErrors: validationErrors ?? Map.from(this.validationErrors),
      hasAutoFilledData: hasAutoFilledData ?? this.hasAutoFilledData,
      storeDefaults: storeDefaults ?? Map.from(this.storeDefaults),
    );
  }
}
