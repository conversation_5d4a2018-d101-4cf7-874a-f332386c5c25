import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/unified_product_management/unified_product_form_bloc.dart';
import 'package:swadesic/features/seller/unified_product_management/components/product_image_manager.dart';
import 'package:swadesic/features/seller/unified_product_management/components/section_navigation_list.dart';
import 'package:swadesic/features/seller/unified_product_management/models/product_form_section.dart';
import 'package:swadesic/features/seller/unified_product_management/sections/basic_details_section.dart';
import 'package:swadesic/features/seller/unified_product_management/sections/inventory_section.dart';
import 'package:swadesic/features/seller/unified_product_management/sections/swadeshi_labels_section.dart';
import 'package:swadesic/model/unified_product_form_data/unified_product_form_data.dart';
import 'package:swadesic/model/unified_product_image_state/unified_product_image_state.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

/// Main unified product management screen
class UnifiedProductManagementScreen extends StatefulWidget {
  final int storeId;
  final String storeReference;
  final String? productReference; // null for add mode, provided for edit mode

  const UnifiedProductManagementScreen({
    Key? key,
    required this.storeId,
    required this.storeReference,
    this.productReference,
  }) : super(key: key);

  @override
  State<UnifiedProductManagementScreen> createState() =>
      _UnifiedProductManagementScreenState();
}

class _UnifiedProductManagementScreenState
    extends State<UnifiedProductManagementScreen> {
  late UnifiedProductFormBloc _formBloc;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _formBloc = UnifiedProductFormBloc(
      context: context,
      storeId: widget.storeId,
      storeReference: widget.storeReference,
      productReference: widget.productReference,
    );
    _initializeForm();
  }

  Future<void> _initializeForm() async {
    try {
      await _formBloc.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      if (mounted) {
        CommonMethods.toastMessage(
          'Failed to initialize form: ${e.toString()}',
          context,
        );
      }
    }
  }

  @override
  void dispose() {
    _formBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: GestureDetector(
        onTap: () => CommonMethods.closeKeyboard(context),
        child: Scaffold(
          backgroundColor: AppColors.appWhite,
          appBar: _buildAppBar(),
          body: _isInitialized ? _buildBody() : _buildLoadingState(),
          bottomNavigationBar: _isInitialized ? _buildBottomBar() : null,
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: _formBloc.isEditMode ? 'Edit Product' : 'Add Product',
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: true,
      textButtonWidget: StreamBuilder<UnifiedProductFormData>(
        stream: _formBloc.formDataStream,
        builder: (context, snapshot) {
          final hasChanges = snapshot.hasData && _formBloc.hasUnsavedChanges;
          return AppCommonWidgets.appBarTextButtonText(
            text: hasChanges ? 'Save Draft' : 'Reset',
          );
        },
      ),
      onTapTextButton: _onTapAppBarAction,
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.brandBlack),
          verticalSizedBox(16),
          Text(
            _formBloc.isEditMode
                ? 'Loading product details...'
                : 'Initializing form...',
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return StreamBuilder<UnifiedProductFormState>(
      stream: _formBloc.formStateStream,
      builder: (context, stateSnapshot) {
        if (stateSnapshot.data == UnifiedProductFormState.loading) {
          return _buildLoadingState();
        }

        if (stateSnapshot.data == UnifiedProductFormState.error) {
          return _buildErrorState();
        }

        return StreamBuilder<UnifiedProductFormData>(
          stream: _formBloc.formDataStream,
          builder: (context, dataSnapshot) {
            if (!dataSnapshot.hasData) {
              return _buildLoadingState();
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProgressIndicator(dataSnapshot.data!),
                  verticalSizedBox(20),
                  _buildImageSection(dataSnapshot.data!),
                  verticalSizedBox(24),
                  _buildSectionNavigation(dataSnapshot.data!),
                  verticalSizedBox(100), // Space for bottom bar
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.writingColor2,
          ),
          verticalSizedBox(16),
          Text(
            'Failed to load form',
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(8),
          Text(
            'Please try again',
            style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
          ),
          verticalSizedBox(16),
          ElevatedButton(
            onPressed: _initializeForm,
            child: Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.brandBlack,
              foregroundColor: AppColors.appWhite,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(UnifiedProductFormData formData) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Form Progress',
                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const Spacer(),
              Text(
                '${formData.completedSectionsCount}/${formData.totalSectionsCount}',
                style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
              ),
            ],
          ),
          verticalSizedBox(8),
          LinearProgressIndicator(
            value: formData.completionPercentage,
            backgroundColor: AppColors.borderColor1,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandBlack),
          ),
          verticalSizedBox(8),
          StreamBuilder<FormValidationResult>(
            stream: _formBloc.validationStream,
            builder: (context, validationSnapshot) {
              if (validationSnapshot.hasData && validationSnapshot.data!.hasErrors) {
                return Row(
                  children: [
                    Icon(
                      Icons.warning_amber_outlined,
                      size: 16,
                      color: Colors.orange,
                    ),
                    horizontalSizedBox(4),
                    Text(
                      '${validationSnapshot.data!.totalErrorCount} issues to resolve',
                      style: AppTextStyle.smallText(textColor: Colors.orange),
                    ),
                  ],
                );
              }
              return Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: AppColors.brandBlack,
                  ),
                  horizontalSizedBox(4),
                  Text(
                    formData.hasRequiredSections
                        ? 'Ready for preview'
                        : 'Complete required sections',
                    style: AppTextStyle.smallText(
                      textColor: formData.hasRequiredSections
                          ? AppColors.brandBlack
                          : AppColors.writingColor2,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(UnifiedProductFormData formData) {
    return StreamBuilder<UnifiedProductImageState>(
      stream: _formBloc.imageStateStream,
      builder: (context, imageSnapshot) {
        final imageState = imageSnapshot.data ?? formData.imageState;
        
        return ProductImageManager(
          imageState: imageState,
          onImageStateChanged: _formBloc.updateImageState,
          maxImages: 10,
          isEditMode: _formBloc.isEditMode,
        );
      },
    );
  }

  Widget _buildSectionNavigation(UnifiedProductFormData formData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Product Information',
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(16),
        StreamBuilder<Map<String, bool>>(
          stream: _formBloc.sectionCompletionStream,
          builder: (context, completionSnapshot) {
            return SectionNavigationList(
              formBloc: _formBloc,
              sectionCompletion: completionSnapshot.data ?? formData.sectionCompletion,
              onSectionTap: _onSectionTap,
            );
          },
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    return StreamBuilder<FormValidationResult>(
      stream: _formBloc.validationStream,
      builder: (context, validationSnapshot) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: StreamBuilder<UnifiedProductFormData>(
                    stream: _formBloc.formDataStream,
                    builder: (context, dataSnapshot) {
                      final canProceed = dataSnapshot.hasData &&
                          dataSnapshot.data!.hasRequiredSections &&
                          (validationSnapshot.data?.isValid ?? false);

                      return ElevatedButton(
                        onPressed: canProceed ? _onTapPreview : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.brandBlack,
                          foregroundColor: AppColors.appWhite,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Preview & Publish',
                          style: AppTextStyle.button2Bold(textColor: AppColors.appWhite),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onSectionTap(ProductFormSectionType sectionType) async {
    final currentData = _formBloc.getSectionData(sectionType);
    Widget? sectionScreen;

    switch (sectionType) {
      case ProductFormSectionType.basicDetails:
        sectionScreen = BasicDetailsSection(
          formBloc: _formBloc,
          initialData: currentData,
        );
        break;
      case ProductFormSectionType.inventory:
        sectionScreen = InventorySection(
          formBloc: _formBloc,
          initialData: currentData,
        );
        break;
      case ProductFormSectionType.swadeshiLabels:
        sectionScreen = SwadeshiLabelsSection(
          formBloc: _formBloc,
          initialData: currentData,
        );
        break;
      default:
        CommonMethods.toastMessage(
          '${sectionType.displayName} section coming soon...',
          context,
        );
        return;
    }

    if (sectionScreen != null) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => sectionScreen!),
      );

      // Refresh the form data after returning from section
      if (result != null) {
        setState(() {
          // Trigger rebuild to show updated data
        });
      }
    }
  }

  void _onTapAppBarAction() {
    if (_formBloc.hasUnsavedChanges) {
      // Save draft functionality
      CommonMethods.toastMessage('Draft saved', context);
    } else {
      // Reset functionality
      _showResetConfirmation();
    }
  }

  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Form'),
        content: Text('Are you sure you want to reset all changes?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset form logic here
              CommonMethods.toastMessage('Form reset', context);
            },
            child: Text('Reset'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  void _onTapPreview() {
    // Validate form one more time
    final validationResult = _formBloc.validateForm();
    
    if (validationResult.isValid) {
      // Navigate to preview screen
      CommonMethods.toastMessage('Navigating to preview...', context);
    } else {
      // Show validation errors
      CommonMethods.toastMessage(
        'Please resolve all issues before proceeding',
        context,
      );
    }
  }

  Future<bool> _onWillPop() async {
    if (_formBloc.hasUnsavedChanges) {
      final shouldPop = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Unsaved Changes'),
          content: Text('You have unsaved changes. Are you sure you want to leave?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Stay'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Leave'),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
            ),
          ],
        ),
      );
      return shouldPop ?? false;
    }
    return true;
  }
}
