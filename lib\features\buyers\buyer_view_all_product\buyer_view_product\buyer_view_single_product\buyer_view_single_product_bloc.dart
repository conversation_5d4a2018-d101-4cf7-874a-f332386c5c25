import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_product_detail/buyer_product_details_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/common_buyer_seller_screen/update_stock/update_stock.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_details/edit_product_details_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/deep_link_response/deep_link_response.dart';
import 'package:swadesic/model/get_buyer_single_product_and_image/get_single_product_detail_response.dart';
import 'package:swadesic/model/get_buyer_single_product_and_image/get_single_product_image_response.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/model/shopping_cart_responses/add_to_cart_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart'
    as products;
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/deep_link/deep_link.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/product_comment_services/product_comment_services.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/single_product_and_image_service/single_product_and_image_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';

import '../../../../../model/shopping_cart_responses/cart_details_response.dart';

enum BuyerViewSingleProductState { Loading, Success, Failed, Deleted }

class BuyerViewSingleProductBloc {
  // region Common Variables
  BuildContext context;
  bool shareBottomSheetVisibility = false;
  List<int> productIdList = [];
  //region Single product
  final String productReference;
  final String productVersion;
  final String productLatestVersion;
  final String subOrderNumber;

  // final bool isProductVersionAvailable;
  ///Single store info
  // late SingleStoreInfoResponse singleStoreInfoResponse;
  // late SingleStoreInfoServices singleStoreInfoServices;
  ///Get Product and image
  late SingleProductAndImageService singleProductAndImageService;
  late GetSingleProductDetailResponse getSingleProductDetailResponse;
  late GetSingleProductImageResponse getSingleProductImageResponse;
  late products.Product finalProduct;
  //endregion
  ///DeepLink
  late DeepLinkServices deepLinkServices;
  late DeepLinkCreateResponse deepLinkCreateResponse;
  String productDeepLink = '';

  ///Get Product All Comment
  late ProductCommentServices productCommentServices;
  late ProductAllCommentResponse productAllCommentResponse;

  ///Add to cart
  late ShoppingCartServices shoppingCartServices;

  ///Cart Items Response
  // late GetCartItemResponses getCartItemResponses;
  late GetCartDetailsResponse cartDetailsResponse;

  ///Cart quantity data model
  late ShoppingCartQuantityDataModel shoppingCartQuantityDataModel;

  ///Add to cart response
  late AddToCartResponse addToCartResponse;

  // endregion
  ///Store Product Services
  late StoreProductServices storeProductServices;

  //region Controller
  final singleProductViewCtrl =
      StreamController<BuyerViewSingleProductState>.broadcast();
  final saveCtrl = StreamController<BuyerViewSingleProductState>.broadcast();
  final sliderCtrl = StreamController<int>.broadcast();
  final shareBottomSheetCtrl = StreamController<bool>.broadcast();
  final commentBottomSheetCtrl = StreamController<bool>.broadcast();
  final buyNowAddCartCtrl = StreamController<bool>.broadcast();
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  BuyerViewSingleProductBloc(this.context, this.productReference,
      this.productVersion, this.productLatestVersion, this.subOrderNumber);
  // endregion

  // region Init
  Future<void> init() async {
    shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);

    //screenRefreshCtrl.sink.add(true);
    singleProductAndImageService = SingleProductAndImageService();
    storeProductServices = StoreProductServices();
    productCommentServices = ProductCommentServices();
    shoppingCartServices = ShoppingCartServices();
    deepLinkServices = DeepLinkServices();

    ///Store
    // singleStoreInfoServices = SingleStoreInfoServices();
    //Get cart
    //Get single product detail
    getSingleProduct();
  }
// endregion

  //region On tap edit details
  void onTapEditDetails(
      {required String productReference, required int storeId}) {
    //Add product reference
    var screen = EditProductDetailsScreen(
      storeId: storeId,
      productReferenceList: [productReference],
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //If null then return
      if (value == null) {
        return;
      } else {
        //Add updated data to
        finalProduct = value;
        //Add product detail to single product details response
        getSingleProductDetailResponse.singleProduct = finalProduct;
        //Add data product image to single product image
        getSingleProductImageResponse.data = finalProduct.prodImages!;
        //Success
        singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Success);
      }
    });
  }
  //endregion

  //region On Change Image Slider
  void onChangeSlider(int index) {
    sliderCtrl.sink.add(index);
  }
//endregion

  //region Save and un-Save Product
  saveUnSaveProduct({required String productReference}) async {
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.saveProduct! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }
    try {
      getSingleProductDetailResponse.singleProduct!.saveStatus =
          !getSingleProductDetailResponse.singleProduct!.saveStatus!;
      saveCtrl.sink.add(BuyerViewSingleProductState.Success);
      // buyerViewStoreProductCtrl.sink.add(BuyerViewStoreState.Loading);
      await storeProductServices.saveUnSaveProduct(productReference);
      //
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Check saved or not
  checkSaveStatus({required productReference}) async {
    //print("check save or not");
    try {
      ///Check if this product view from order then don't check is it saved or not
      // if(productVersion != null){
      //   return;
      // }
      saveCtrl.sink.add(BuyerViewSingleProductState.Success);
      // buyerViewStoreProductCtrl.sink.add(BuyerViewStoreState.Loading);
      getSingleProductDetailResponse.singleProduct!.saveStatus =
          await storeProductServices.checkProductSave(
              productReference: productReference);
      saveCtrl.sink.add(BuyerViewSingleProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Go to Buyer Product Detail Screen
  void goToBuyerProductDetail() {
    var screen = BuyerProductDetailsScreen(
      product: finalProduct,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to Buyer Image Preview Screen
  void goToBuyerProductImageScreen(var productImage, int index) {
    List<String> imageUrls = [];
    for (var data in productImage) {
      imageUrls.add(data.productImage!);
    }

    var screen = BuyerImagePreviewScreen(
      productImage: imageUrls,
      imageIndex: index,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }
  //endregion

  //region On Tap Share
  void onTapShare({required String? imageUrl}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        entityType: EntityType.PRODUCT,
        objectReference: productReference,
        url: AppLinkCreateService()
            .createProductLink(productReference: productReference),
        imageLink: imageUrl,
        imageType: CustomImageContainerType.product,
        message: "Check out this product.",
      ),
      context: context,
    );
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }
  //endregion

  //region View comment
  viewComment({required String productRef, required String storeReference}) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewCommentQuestionReview! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }
    var screen = SinglePostViewScreen(
      postReference: productRef,
      isFromProductScreen: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
    return;
    // return context.mounted?CommonMethods.toastMessage(AppStrings.thisFeatureIsCommingSoon, context):null;
    // var screen = BuyerProductCommentScreen(
    //   productRef: productRef,
    //   storeReference: storeReference,
    //   isWriteComment: isWriteComment,
    // );
    // var route = CupertinoPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
  //endregion

  //region View comment
  addComment(
      {required String productRef,
      required String storeReference,
      required int productId,
      required bool isWriteComment}) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.addComment! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }
    var screen = SinglePostViewScreen(
      postReference: productRef,
      isFromProductScreen: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
    return;
    // return context.mounted?CommonMethods.toastMessage(AppStrings.thisFeatureIsCommingSoon, context):null;
    //
    // //Todo
    // var screen = BuyerProductCommentScreen(
    //   productRef: productRef,
    //   storeReference: storeReference,
    //   isWriteComment: isWriteComment,
    // );
    // var route = CupertinoPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
  //endregion

  //region On tap Buy not / Add to Cart
  void onTapBuyAddCart(
    int storeId,
    int productId,
  ) {
    buyNowAddCartCtrl.sink.add(true);
    CommonMethods.toastMessage("Item has been added to your cart", context);

    ///addToCart(storeId,productId);
    gotoShoppingCart();
  }

  //endregion

  //region On tap drawer
  void onTapDrawer({required String productReference}) async {
    List<Map<String, dynamic>> accessOptions = [
      {
        'title': AppStrings.copyProductLink,
        'onTap': () {
          Navigator.pop(context);
          CommonMethods.copyText(
              context,
              AppLinkCreateService()
                  .createProductLink(productReference: productReference));
        },
      },
      {
        'title': AppStrings.shareTheProduct,
        'onTap': () {
          Navigator.pop(context);
          ProductDetailFullCardBloc(context, finalProduct, false).onTapShare(
              imageUrl: finalProduct.prodImages?.first.productImage,
              productReference: productReference);
        },
      },
      {
        'title': AppStrings.reportTheProduct,
        'onTap': () {
          Navigator.pop(context);
          // Navigator.pop(context);
          var screen = ReportScreen(
            reference: productReference,
            isProduct: true,
          );
          var route = MaterialPageRoute(builder: (context) => screen);
          Navigator.push(context, route);

//endregion
        },
      },
      // Add more options if needed
    ];

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }
  //endregion

  //region On tap drawer
  void openDrawer({required String url}) async {
    CommonMethods.appBottomSheet(
        screen: SingleChildScrollView(child: Container()), context: context);
  }

//endregion

  //region Add to cart
  // addToCart(int storeId,int productId,)async{
  addToCart({bool goToCart = false}) async {
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.buyProduct! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }

    ///Check is the store reference is contains in store list created by user.
    ///If not then return "You have to purchases from own store."
    //If empty store list
    if (BuyerHomeBloc.storeListResponse.storeList!.isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.youCanOnlyOrderFromOtherStoreAfterOfficialLaunch, context,
          toastShowTimer: 10);
    }
    //If store reference does not contains in list
    if (BuyerHomeBloc.storeListResponse.storeList!
        .where((element) =>
            element.storeReference! ==
            getSingleProductDetailResponse.singleProduct!.storeReference!)
        .isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.youCanOnlyOrderFromOtherStoreAfterOfficialLaunch, context,
          toastShowTimer: 10);
    }

    try {
      addToCartResponse = await shoppingCartServices.addToCart(
          productReference: productReference,
          storeId: getSingleProductDetailResponse.singleProduct!.storeid!);

      ///Add product reference to the data model
      shoppingCartQuantityDataModel.updateCartQuantity(
          productReference: productReference);

      // AppConstants.cartItemIdList.add(addToCartResponse.data!.cartitemid!);
      // getCartDetails();
      // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
      //
      // //print(AppConstants.cartItemIdList);
      CommonMethods.toastMessage(
          'Product has been added to your cart', context);
      screenRefreshCtrl.sink.add(true);
      if (goToCart) {
        gotoShoppingCart();
      }
      return;

      //Refresh ui

      // buyerViewProductImageCtrl.sink.add(BuyerViewProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    }
  }
//endregion

//region Go to Shopping Cart Screen
  gotoShoppingCart() {
    // AppConstants.userLevelPersistentTabController.jumpToTab(2);
    // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);

    // AppConstants.cartItemIdList.clear();
    var screen = const ShoppingCartScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      screenRefreshCtrl.sink.add(true);
    }).then((value) {});
  }
//endregion

  //region Switch to buyer
  void switchToBuyer({required String productReference}) async {
    //Seller view to false
    AppConstants.appData.isStoreView = false;
    //User view true
    AppConstants.appData.isUserView = true;
    //Push User level bottom navigation and replace previous bottom navigation
    Navigator.pushReplacement(
      AppConstants.userStoreCommonBottomNavigationContext,
      MaterialPageRoute(builder: (context) => const UserBottomNavigation()),
    );
    //Make initial tab to index 0
    AppConstants.userPersistentTabController.index = 0;
    await Future.delayed(const Duration(seconds: 2));
    //Push to product screen
    var screen =
        BuyerViewSingleProductScreen(productReference: productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
  //endregion

//region Dispose
  void dispose() {
    imageCache.clear();
    sliderCtrl.close();
    singleProductViewCtrl.close();
    shareBottomSheetCtrl.close();
    commentBottomSheetCtrl.close();
    // AppConstants.cartItemIdList.clear();
  }
//endregion

  ///Single product api call
//region Single product Api Call
  Future<void> getSingleProduct() async {
    try {
      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Loading);
      //If version is empty
      if (productVersion.isEmpty) {
        getSingleProductDetailResponse =
            await singleProductAndImageService.getSingleProductInfo(
                productReference: productReference,
                pinCode: AppConstants.appData.isUserView!
                    ? BuyerHomeBloc.userDetailsResponse.userDetail!.pincode!
                    : AppConstants.appData.pinCode!);
      }
      //If version is not empty then sub order number is there
      else {
        getSingleProductDetailResponse = await singleProductAndImageService
            .getSingleProductInfoFromOrder(subOrderNumber: subOrderNumber);
      }

      ///After getting detail add to final product
      finalProduct = getSingleProductDetailResponse.singleProduct!;

      //Is store deleted
      // if(getSingleProductDetailResponse.singleProduct!.isDeleted!){
      //   singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Deleted);
      //   return;
      // }
      ///Product images
      getSingleProductImage();
      // singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Failed);
      return;
    }
  }

//endregion

//region Single product image Api Call
  getSingleProductImage() async {
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    try {
      // singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Loading);
      getSingleProductImageResponse = await singleProductAndImageService
          .getSingleProductImage(productReference, productVersion);

      ///After getting detail add to final product
      finalProduct.prodImages = getSingleProductImageResponse.data!;

      // Update the global product data model with the latest product details
      // Find the product in the global model and update it at the same index
      var existingIndex = productDataModel.allProducts.indexWhere((product) =>
          product.productReference == finalProduct.productReference);

      if (existingIndex != -1) {
        // Update the product at the same index
        productDataModel.allProducts[existingIndex] = finalProduct;
      } else {
        // If not found, add it to the list
        productDataModel.addProductIntoList(products: [finalProduct]);
      }

      //print("length ${getSingleProductImageResponse.data!.length}");
      //Success
      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

// region Go to Single product screen
  goToSingleProductScreen({required String productReference}) {
    var screen =
        BuyerViewSingleProductScreen(productReference: productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to store screen
  void goToStore({required String storeReference}) {
    var screen = BuyerViewStoreScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Open update stocks
  void onTapUpdateStock({required Product product}) async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(20), topLeft: Radius.circular(20))),
        builder: (context) {
          return SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Text(
                      AppStrings.updateStock,
                      style: AppTextStyle.sectionSemiBold(
                          textColor: AppColors.appBlack),
                    ),
                  ),
                  UpdateStock(
                    product: product,
                  ),
                ],
              ));
        }).then((value) {});
  }
//endregion

  //region On tap heart
  Future<void> onTapHeart({required Product product}) async {
    try {
      //Update liked count and is liked
      //Toggle the like status
      product.likeStatus = !product.likeStatus!;
      if (product.likeStatus!) {
        product.likeCount = (product.likeCount ?? 0) + 1;
      } else {
        product.likeCount = (product.likeCount ?? 0) - 1;
      }

      singleProductViewCtrl.sink.add(BuyerViewSingleProductState.Success);

      //Api call
      await PostService().likePost(
          postReference: product.productReference!,
          likeStatus: product.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      return;
    }
  }
//endregion
}
