import 'package:flutter/material.dart';

/// Abstract base class for product form sections
abstract class ProductFormSection {
  /// Unique identifier for the section
  String get sectionId;
  
  /// Display name for the section
  String get sectionName;
  
  /// Description of the section
  String get sectionDescription;
  
  /// Icon for the section
  IconData get sectionIcon;
  
  /// Whether this section is required for product creation
  bool get isRequired;
  
  /// Whether this section is completed
  bool get isCompleted;
  
  /// Current section data
  Map<String, dynamic> get sectionData;
  
  /// Validation errors for this section
  List<String> get validationErrors;
  
  /// Build the section tile for the main form
  Widget buildSectionTile(BuildContext context, VoidCallback onTap);
  
  /// Build the section screen for detailed editing
  Widget buildSectionScreen(BuildContext context);
  
  /// Reset section to defaults
  void resetToDefaults();
  
  /// Save section data to cache
  Future<void> saveToCache();
  
  /// Load section data from cache
  Future<void> loadFromCache();
  
  /// Validate section data
  List<String> validate();
  
  /// Update section data
  void updateData(Map<String, dynamic> data);
}

/// Enum for product form sections
enum ProductFormSectionType {
  basicDetails,
  inventory,
  swadeshiLabels,
  fulfillmentSettings,
  returnSettings,
  productPromotions,
  visibility,
  moreDetails,
}

/// Extension for section type properties
extension ProductFormSectionTypeExtension on ProductFormSectionType {
  String get id {
    switch (this) {
      case ProductFormSectionType.basicDetails:
        return 'basicDetails';
      case ProductFormSectionType.inventory:
        return 'inventory';
      case ProductFormSectionType.swadeshiLabels:
        return 'swadeshiLabels';
      case ProductFormSectionType.fulfillmentSettings:
        return 'fulfillmentSettings';
      case ProductFormSectionType.returnSettings:
        return 'returnSettings';
      case ProductFormSectionType.productPromotions:
        return 'productPromotions';
      case ProductFormSectionType.visibility:
        return 'visibility';
      case ProductFormSectionType.moreDetails:
        return 'moreDetails';
    }
  }

  String get displayName {
    switch (this) {
      case ProductFormSectionType.basicDetails:
        return 'Basic Details';
      case ProductFormSectionType.inventory:
        return 'Inventory';
      case ProductFormSectionType.swadeshiLabels:
        return 'Swadeshi Labels';
      case ProductFormSectionType.fulfillmentSettings:
        return 'Fulfillment Settings';
      case ProductFormSectionType.returnSettings:
        return 'Return Settings';
      case ProductFormSectionType.productPromotions:
        return 'Product Promotions';
      case ProductFormSectionType.visibility:
        return 'Visibility';
      case ProductFormSectionType.moreDetails:
        return 'More Details';
    }
  }

  String get description {
    switch (this) {
      case ProductFormSectionType.basicDetails:
        return 'Product name, brand, category, and description';
      case ProductFormSectionType.inventory:
        return 'Stock, pricing, and product variants';
      case ProductFormSectionType.swadeshiLabels:
        return 'Swadeshi brand, made, and owned labels';
      case ProductFormSectionType.fulfillmentSettings:
        return 'Delivery and pickup options';
      case ProductFormSectionType.returnSettings:
        return 'Return and warranty policies';
      case ProductFormSectionType.productPromotions:
        return 'Affiliate promotions and commissions';
      case ProductFormSectionType.visibility:
        return 'Product slug, code, tags, and targeting';
      case ProductFormSectionType.moreDetails:
        return 'Additional product information';
    }
  }

  IconData get icon {
    switch (this) {
      case ProductFormSectionType.basicDetails:
        return Icons.info_outline;
      case ProductFormSectionType.inventory:
        return Icons.inventory_2_outlined;
      case ProductFormSectionType.swadeshiLabels:
        return Icons.label_outline;
      case ProductFormSectionType.fulfillmentSettings:
        return Icons.local_shipping_outlined;
      case ProductFormSectionType.returnSettings:
        return Icons.assignment_return_outlined;
      case ProductFormSectionType.productPromotions:
        return Icons.campaign_outlined;
      case ProductFormSectionType.visibility:
        return Icons.visibility_outlined;
      case ProductFormSectionType.moreDetails:
        return Icons.more_horiz_outlined;
    }
  }

  bool get isRequired {
    switch (this) {
      case ProductFormSectionType.basicDetails:
      case ProductFormSectionType.swadeshiLabels:
        return true;
      case ProductFormSectionType.inventory:
      case ProductFormSectionType.fulfillmentSettings:
      case ProductFormSectionType.returnSettings:
      case ProductFormSectionType.productPromotions:
      case ProductFormSectionType.visibility:
      case ProductFormSectionType.moreDetails:
        return false;
    }
  }

  int get order {
    switch (this) {
      case ProductFormSectionType.basicDetails:
        return 0;
      case ProductFormSectionType.inventory:
        return 1;
      case ProductFormSectionType.swadeshiLabels:
        return 2;
      case ProductFormSectionType.fulfillmentSettings:
        return 3;
      case ProductFormSectionType.returnSettings:
        return 4;
      case ProductFormSectionType.productPromotions:
        return 5;
      case ProductFormSectionType.visibility:
        return 6;
      case ProductFormSectionType.moreDetails:
        return 7;
    }
  }
}

/// Section completion status
enum SectionCompletionStatus {
  notStarted,
  inProgress,
  completed,
  hasErrors,
}

/// Section completion info
class SectionCompletionInfo {
  final ProductFormSectionType sectionType;
  final SectionCompletionStatus status;
  final List<String> errors;
  final DateTime? lastUpdated;

  SectionCompletionInfo({
    required this.sectionType,
    required this.status,
    this.errors = const [],
    this.lastUpdated,
  });

  bool get isCompleted => status == SectionCompletionStatus.completed;
  bool get hasErrors => status == SectionCompletionStatus.hasErrors || errors.isNotEmpty;
  bool get isRequired => sectionType.isRequired;

  SectionCompletionInfo copyWith({
    ProductFormSectionType? sectionType,
    SectionCompletionStatus? status,
    List<String>? errors,
    DateTime? lastUpdated,
  }) {
    return SectionCompletionInfo(
      sectionType: sectionType ?? this.sectionType,
      status: status ?? this.status,
      errors: errors ?? this.errors,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// Form validation result
class FormValidationResult {
  final bool isValid;
  final Map<ProductFormSectionType, List<String>> sectionErrors;
  final List<String> globalErrors;

  FormValidationResult({
    required this.isValid,
    this.sectionErrors = const {},
    this.globalErrors = const [],
  });

  bool get hasErrors => !isValid || sectionErrors.isNotEmpty || globalErrors.isNotEmpty;
  
  int get totalErrorCount {
    int count = globalErrors.length;
    for (final errors in sectionErrors.values) {
      count += errors.length;
    }
    return count;
  }

  List<String> getErrorsForSection(ProductFormSectionType sectionType) {
    return sectionErrors[sectionType] ?? [];
  }

  bool sectionHasErrors(ProductFormSectionType sectionType) {
    return getErrorsForSection(sectionType).isNotEmpty;
  }
}
